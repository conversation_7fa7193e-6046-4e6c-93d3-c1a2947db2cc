"""
Alapanyag panel modul
Kezeli az alapanyagok (szabványlapok és maradékok) megjelenítését és kezelését
"""

import tkinter as tk
from tkinter import ttk
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import uuid


@dataclass
class MaterialPiece:
    """Alapanyag darab adatstruktúra"""
    id: str
    name: str
    width: float
    height: float
    color: str
    material_type: str  # "standard" vagy "remnant"
    original_sheet_id: str = None  # Eredeti lap ID (maradékoknál)
    position_in_panel: Tuple[float, float] = (0, 0)  # Pozíció a panelben


class MaterialPanel:
    """Alapanyag panel osztály"""
    
    def __init__(self, parent_frame, canvas_manager):
        self.parent_frame = parent_frame
        self.canvas_manager = canvas_manager
        self.materials: List[MaterialPiece] = []
        self.active = False
        self.selected_material = None
        self.dragging_material = None
        
        # Panel UI elemek
        self.panel_frame = None
        self.canvas = None
        self.scrollbar = None
        self.material_items = {}  # material_id -> canvas item mapping
        
        # Skálázási beállítások
        self.panel_width = 200
        self.item_height = 80
        self.item_margin = 10
        self.scale_factor = 1.0
        
        self._create_panel()
    
    def _create_panel(self):
        """Alapanyag panel UI létrehozása"""
        # Fő frame
        self.panel_frame = ttk.LabelFrame(
            self.parent_frame, 
            text="Alapanyagok", 
            padding="5"
        )
        self.panel_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=False, padx=5)
        
        # Állapot label
        self.status_label = ttk.Label(
            self.panel_frame, 
            text="Panel inaktív", 
            font=("Arial", 9, "italic")
        )
        self.status_label.pack(pady=(0, 5))
        
        # Scrollable canvas
        canvas_frame = ttk.Frame(self.panel_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.canvas = tk.Canvas(
            canvas_frame,
            width=self.panel_width,
            height=400,
            bg="#f0f0f0",
            highlightthickness=1,
            highlightbackground="#cccccc"
        )
        
        self.scrollbar = ttk.Scrollbar(
            canvas_frame, 
            orient=tk.VERTICAL, 
            command=self.canvas.yview
        )
        
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Canvas események
        self.canvas.bind("<Button-1>", self._on_canvas_click)
        self.canvas.bind("<B1-Motion>", self._on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_canvas_release)
        self.canvas.bind("<Motion>", self._on_canvas_motion)
        
        # Kezdetben inaktív
        self.set_active(False)
    
    def set_active(self, active: bool):
        """Panel aktív/inaktív állapot beállítása"""
        self.active = active
        
        if active:
            self.status_label.config(text="Panel aktív - húzd az alapanyagokat!", foreground="green")
            self.panel_frame.config(text="Alapanyagok (AKTÍV)")
        else:
            self.status_label.config(text="Válassz ki egy panelt a rajzon!", foreground="gray")
            self.panel_frame.config(text="Alapanyagok")
        
        # Canvas állapot
        if active:
            self.canvas.config(cursor="hand2")
        else:
            self.canvas.config(cursor="")
    
    def add_material(self, material: MaterialPiece):
        """Alapanyag hozzáadása"""
        self.materials.append(material)
        self._update_display()
    
    def remove_material(self, material_id: str):
        """Alapanyag eltávolítása"""
        self.materials = [m for m in self.materials if m.id != material_id]
        self._update_display()
    
    def add_standard_sheet(self, name: str, width: float, height: float, color: str):
        """Szabványlap hozzáadása"""
        material = MaterialPiece(
            id=str(uuid.uuid4()),
            name=name,
            width=width,
            height=height,
            color=color,
            material_type="standard"
        )
        self.add_material(material)
        return material
    
    def add_remnant(self, name: str, width: float, height: float, color: str, original_sheet_id: str):
        """Maradék darab hozzáadása"""
        material = MaterialPiece(
            id=str(uuid.uuid4()),
            name=f"{name} (maradék)",
            width=width,
            height=height,
            color=color,
            material_type="remnant",
            original_sheet_id=original_sheet_id
        )
        self.add_material(material)
        return material
    
    def _update_display(self):
        """Megjelenítés frissítése"""
        # Canvas törlése
        self.canvas.delete("all")
        self.material_items = {}
        
        if not self.materials:
            # Üres állapot
            self.canvas.create_text(
                self.panel_width // 2, 50,
                text="Nincs alapanyag\nAdj hozzá szabványlapokat!",
                fill="gray",
                font=("Arial", 10),
                justify=tk.CENTER
            )
            return
        
        # Skálázási tényező frissítése
        self._update_scale_factor()
        
        # Alapanyagok rajzolása
        y_offset = self.item_margin
        
        for i, material in enumerate(self.materials):
            item_y = y_offset
            self._draw_material_item(material, item_y)
            y_offset += self.item_height + self.item_margin
        
        # Scroll régió beállítása
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def _update_scale_factor(self):
        """Skálázási tényező frissítése a fő canvas zoom szintje alapján"""
        if self.canvas_manager:
            # Fő canvas zoom szintje
            main_scale = self.canvas_manager.scale
            # Alapanyag panel skálázása (kisebb, hogy elférjen)
            self.scale_factor = min(0.1, 50.0 / main_scale)
        else:
            self.scale_factor = 0.1
    
    def _draw_material_item(self, material: MaterialPiece, y_pos: float):
        """Egyetlen alapanyag elem rajzolása"""
        # Skálázott méretek
        scaled_width = material.width * self.scale_factor
        scaled_height = material.height * self.scale_factor
        
        # Pozíció számítása (középre igazítva)
        x_center = self.panel_width // 2
        item_x = x_center - scaled_width // 2
        item_y = y_pos
        
        # Alapanyag téglalap
        rect_id = self.canvas.create_rectangle(
            item_x, item_y,
            item_x + scaled_width, item_y + scaled_height,
            fill=material.color,
            outline="#333333",
            width=2,
            tags=f"material_{material.id}"
        )
        
        # Szöveg információ
        text_y = item_y + scaled_height + 15
        
        # Név
        name_id = self.canvas.create_text(
            x_center, text_y,
            text=material.name,
            font=("Arial", 8, "bold"),
            anchor=tk.CENTER,
            tags=f"material_{material.id}"
        )
        
        # Méretek
        size_text = f"{material.width:.0f}×{material.height:.0f}"
        size_id = self.canvas.create_text(
            x_center, text_y + 12,
            text=size_text,
            font=("Arial", 7),
            fill="gray",
            anchor=tk.CENTER,
            tags=f"material_{material.id}"
        )
        
        # Típus jelölő
        if material.material_type == "remnant":
            type_id = self.canvas.create_text(
                item_x + 5, item_y + 5,
                text="M",  # Maradék
                font=("Arial", 8, "bold"),
                fill="red",
                anchor=tk.NW,
                tags=f"material_{material.id}"
            )
        
        # Tárolás
        self.material_items[material.id] = {
            'rect': rect_id,
            'name': name_id,
            'size': size_id,
            'material': material,
            'bounds': (item_x, item_y, item_x + scaled_width, item_y + scaled_height)
        }
        
        # Pozíció tárolása
        material.position_in_panel = (item_x, item_y)
    
    def _on_canvas_click(self, event):
        """Canvas kattintás eseménykezelő"""
        if not self.active:
            return
        
        # Melyik alapanyagra kattintottunk?
        clicked_material = self._get_material_at_position(event.x, event.y)
        
        if clicked_material:
            self.selected_material = clicked_material
            self._highlight_material(clicked_material.id, True)
            
            # Drag kezdése
            self.dragging_material = clicked_material
            self.drag_start_x = event.x
            self.drag_start_y = event.y
    
    def _on_canvas_drag(self, event):
        """Canvas húzás eseménykezelő"""
        if not self.active or not self.dragging_material:
            return
        
        # Ha elég messzire húztuk, akkor átadjuk a fő canvas-nak
        dx = event.x - self.drag_start_x
        dy = event.y - self.drag_start_y
        distance = (dx * dx + dy * dy) ** 0.5
        
        if distance > 20:  # 20 pixel után kezdődik a drag
            self._start_main_canvas_drag()
    
    def _on_canvas_release(self, event):
        """Canvas felengedés eseménykezelő"""
        if self.selected_material:
            self._highlight_material(self.selected_material.id, False)
        
        self.selected_material = None
        self.dragging_material = None
    
    def _on_canvas_motion(self, event):
        """Canvas egér mozgás eseménykezelő"""
        if not self.active:
            return
        
        # Hover effekt
        hovered_material = self._get_material_at_position(event.x, event.y)
        
        if hovered_material:
            self.canvas.config(cursor="hand2")
        else:
            self.canvas.config(cursor="")
    
    def _get_material_at_position(self, x: float, y: float) -> Optional[MaterialPiece]:
        """Alapanyag keresése pozíció alapján"""
        for material_id, item_data in self.material_items.items():
            bounds = item_data['bounds']
            if bounds[0] <= x <= bounds[2] and bounds[1] <= y <= bounds[3]:
                return item_data['material']
        return None
    
    def _highlight_material(self, material_id: str, highlight: bool):
        """Alapanyag kiemelése"""
        if material_id in self.material_items:
            rect_id = self.material_items[material_id]['rect']
            if highlight:
                self.canvas.itemconfig(rect_id, outline="#FF0000", width=3)
            else:
                self.canvas.itemconfig(rect_id, outline="#333333", width=2)
    
    def _start_main_canvas_drag(self):
        """Fő canvas-ra való drag kezdése"""
        if not self.dragging_material:
            return
        
        # Esemény küldése a fő alkalmazásnak
        if hasattr(self, 'drag_callback'):
            self.drag_callback(self.dragging_material)
    
    def set_drag_callback(self, callback):
        """Drag callback beállítása"""
        self.drag_callback = callback
    
    def update_scale(self):
        """Skálázás frissítése (fő canvas zoom változásakor)"""
        self._update_display()
    
    def get_materials(self) -> List[MaterialPiece]:
        """Összes alapanyag visszaadása"""
        return self.materials.copy()
    
    def clear_materials(self):
        """Összes alapanyag törlése"""
        self.materials = []
        self._update_display()
