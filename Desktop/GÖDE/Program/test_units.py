"""
Mértékegység kezelés tesztelése
"""

from dxf_parser import DXFParser
from unit_manager import UnitManager


def test_unit_scaling():
    """Mértékegység skálázás tesztelése"""
    
    print("=== MÉRTÉKEGYSÉG SKÁLÁZÁS TESZT ===\n")
    
    # Unit manager létrehozása
    unit_manager = UnitManager()
    
    # Különböző mértékegységek tesztelése
    test_units = ["mm", "cm", "m", "inch"]
    
    for unit in test_units:
        print(f"--- {unit.upper()} EGYSÉG TESZT ---")
        
        # Mértékegység beállítása
        unit_manager.set_unit(unit)
        scale_factor = unit_manager.get_scale_factor()
        
        print(f"Skálázási tényező: {scale_factor}")
        
        # DXF parser létrehozása
        parser = DXFParser(unit_manager)
        
        # DXF betöltése a skálázási tényezővel
        success = parser.load_dxf("GODEFEHER.dxf", scale_factor)
        
        if success:
            entities = parser.get_entities()
            bounds = parser.get_bounds()
            
            # Eredmények
            width_mm = bounds[2] - bounds[0]
            height_mm = bounds[3] - bounds[1]
            
            # Konvertálás az eredeti egységre
            width_original = unit_manager.convert_from_mm(width_mm)
            height_original = unit_manager.convert_from_mm(height_mm)
            
            print(f"Rajz mérete mm-ben: {width_mm:.1f} x {height_mm:.1f}")
            print(f"Rajz mérete {unit}-ben: {width_original:.1f} x {height_original:.1f}")
            
            # Első vonal hossza
            first_line = None
            for entity in entities:
                if entity['type'] == 'line':
                    first_line = entity
                    break
            
            if first_line:
                dx = first_line['end'][0] - first_line['start'][0]
                dy = first_line['end'][1] - first_line['start'][1]
                length_mm = (dx*dx + dy*dy)**0.5
                length_original = unit_manager.convert_from_mm(length_mm)
                
                print(f"Első vonal hossza mm-ben: {length_mm:.2f}")
                print(f"Első vonal hossza {unit}-ben: {length_original:.2f}")
            
            print(f"✅ {unit} egység teszt sikeres")
        else:
            print(f"❌ {unit} egység teszt sikertelen")
        
        print()
    
    # Összehasonlítás
    print("=== ÖSSZEHASONLÍTÁS ===")
    print("Ha a DXF-ben egy érték 120:")
    
    for unit in test_units:
        unit_manager.set_unit(unit)
        mm_value = unit_manager.convert_to_mm(120.0)
        formatted = unit_manager.format_value(mm_value)
        print(f"  {unit}: 120 → {mm_value:.1f} mm → {formatted}")


def test_coordinate_display():
    """Koordináta megjelenítés tesztelése"""
    
    print("\n=== KOORDINÁTA MEGJELENÍTÉS TESZT ===\n")
    
    unit_manager = UnitManager()
    
    # Teszt koordináták mm-ben
    test_coords = [
        (100.0, 200.0),
        (1500.0, 2500.0),
        (50.5, 75.3),
        (0.0, 0.0)
    ]
    
    for unit in ["mm", "cm", "m", "inch"]:
        unit_manager.set_unit(unit)
        print(f"--- {unit.upper()} EGYSÉGBEN ---")
        
        for x_mm, y_mm in test_coords:
            x_formatted = unit_manager.format_value(x_mm, 1)
            y_formatted = unit_manager.format_value(y_mm, 1)
            print(f"  ({x_mm:.1f}, {y_mm:.1f}) mm → X: {x_formatted}, Y: {y_formatted}")
        
        print()


if __name__ == "__main__":
    test_unit_scaling()
    test_coordinate_display()
