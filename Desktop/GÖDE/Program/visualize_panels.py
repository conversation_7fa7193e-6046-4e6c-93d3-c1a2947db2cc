"""
Panel vizualizáció tesztelése
"""

import matplotlib.pyplot as plt
from dxf_parser import DXFParser
from panel_manager import PanelManager


def visualize_panels():
    """Panelek vizualizálása matplotlib-tal"""
    
    # DXF betöltése
    parser = DXFParser()
    success = parser.load_dxf("GODEFEHER.dxf")
    
    if not success:
        print("Hiba: Nem sikerült betölteni a DXF fájlt!")
        return
    
    entities = parser.get_entities()
    
    # Panel manager
    panel_manager = PanelManager()
    panels = panel_manager.find_closed_shapes(entities)
    
    # Matplotlib ábra
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 8))
    
    # Bal oldal: Összes vonal
    ax1.set_title("Összes vonal")
    ax1.set_aspect('equal')
    ax1.grid(True, alpha=0.3)
    
    for entity in entities:
        if entity['type'] == 'line':
            start = entity['start']
            end = entity['end']
            ax1.plot([start[0], end[0]], [start[1], end[1]], 'b-', linewidth=1)
    
    # Jobb oldal: Felismert panelek
    ax2.set_title(f"Felismert panelek ({len(panels)} db)")
    ax2.set_aspect('equal')
    ax2.grid(True, alpha=0.3)
    
    colors = ['red', 'green', 'blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']
    
    for i, panel in enumerate(panels):
        color = colors[i % len(colors)]
        
        # Panel vonalai
        for entity in panel.entities:
            if entity['type'] == 'line':
                start = entity['start']
                end = entity['end']
                ax2.plot([start[0], end[0]], [start[1], end[1]], color=color, linewidth=2)
        
        # Panel középpont és ID
        center = panel.center
        ax2.plot(center[0], center[1], 'ko', markersize=8)
        ax2.text(center[0], center[1], f'P{panel.id}', 
                ha='center', va='center', fontsize=8, fontweight='bold', color='white')
        
        # Panel információ
        print(f"Panel {panel.id}: Terület={panel.area:.0f}, Kerület={panel.perimeter:.0f}")
    
    # Tengelyek beállítása
    bounds = parser.get_bounds()
    for ax in [ax1, ax2]:
        ax.set_xlim(bounds[0] - 50, bounds[2] + 50)
        ax.set_ylim(bounds[1] - 50, bounds[3] + 50)
        ax.set_xlabel('X koordináta')
        ax.set_ylabel('Y koordináta')
    
    plt.tight_layout()
    plt.savefig('panel_visualization.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"\nÖsszesen {len(panels)} panel található a DXF fájlban.")
    print("A vizualizáció elmentve: panel_visualization.png")


if __name__ == "__main__":
    try:
        visualize_panels()
    except ImportError:
        print("Matplotlib nincs telepítve. Telepítés: pip install matplotlib")
    except Exception as e:
        print(f"Hiba: {e}")
