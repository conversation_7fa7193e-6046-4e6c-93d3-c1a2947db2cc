"""
Teszt DXF fájl létrehozása a DXF Viewer teszteléséhez
"""

import ezdxf
import math


def create_test_dxf():
    """Egyszerű teszt DXF fájl létrehozása"""
    
    # Új DXF dokumentum létrehozása
    doc = ezdxf.new('R2010')
    
    # Modelspace elérése
    msp = doc.modelspace()
    
    # Rétegek létrehozása
    doc.layers.new('VONALAK', dxfattribs={'color': 1})  # Piros
    doc.layers.new('KOROK', dxfattribs={'color': 2})    # Sárga
    doc.layers.new('SZOVEG', dxfattribs={'color': 3})   # Zöld
    doc.layers.new('GEOMETRIA', dxfattribs={'color': 4}) # Cián
    
    # Vonalak rajzolása - egyszerű négyzet
    square_size = 100
    msp.add_line((0, 0), (square_size, 0), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((square_size, 0), (square_size, square_size), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((square_size, square_size), (0, square_size), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((0, square_size), (0, 0), dxfattribs={'layer': 'VONALAK'})
    
    # Átlók
    msp.add_line((0, 0), (square_size, square_size), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((0, square_size), (square_size, 0), dxfattribs={'layer': 'VONALAK'})
    
    # Körök
    center_x, center_y = square_size / 2, square_size / 2
    msp.add_circle((center_x, center_y), 30, dxfattribs={'layer': 'KOROK'})
    msp.add_circle((center_x, center_y), 20, dxfattribs={'layer': 'KOROK'})
    msp.add_circle((center_x, center_y), 10, dxfattribs={'layer': 'KOROK'})
    
    # Ívek
    msp.add_arc((center_x, center_y), 40, 0, 90, dxfattribs={'layer': 'GEOMETRIA'})
    msp.add_arc((center_x, center_y), 40, 180, 270, dxfattribs={'layer': 'GEOMETRIA'})
    
    # Polyline - csillag alakzat
    star_points = []
    num_points = 8
    outer_radius = 60
    inner_radius = 25
    
    for i in range(num_points * 2):
        angle = i * math.pi / num_points
        if i % 2 == 0:
            radius = outer_radius
        else:
            radius = inner_radius
        
        x = center_x + radius * math.cos(angle)
        y = center_y + radius * math.sin(angle)
        star_points.append((x, y))
    
    msp.add_lwpolyline(star_points, close=True, dxfattribs={'layer': 'GEOMETRIA'})
    
    # Szövegek
    msp.add_text("DXF Viewer Teszt", dxfattribs={
        'layer': 'SZOVEG',
        'height': 8,
        'insert': (10, square_size + 10)
    })
    
    msp.add_text("Négyzet", dxfattribs={
        'layer': 'SZOVEG',
        'height': 5,
        'insert': (center_x - 15, -15)
    })
    
    msp.add_text("Körök", dxfattribs={
        'layer': 'SZOVEG',
        'height': 4,
        'insert': (center_x - 8, center_y)
    })
    
    # További geometriai elemek
    # Kis négyzetek a sarkoknál
    corner_size = 10
    corners = [(0, 0), (square_size, 0), (square_size, square_size), (0, square_size)]
    
    for i, (cx, cy) in enumerate(corners):
        # Kis négyzet minden saroknál
        offset = corner_size / 2
        msp.add_line((cx - offset, cy - offset), (cx + offset, cy - offset), dxfattribs={'layer': 'GEOMETRIA'})
        msp.add_line((cx + offset, cy - offset), (cx + offset, cy + offset), dxfattribs={'layer': 'GEOMETRIA'})
        msp.add_line((cx + offset, cy + offset), (cx - offset, cy + offset), dxfattribs={'layer': 'GEOMETRIA'})
        msp.add_line((cx - offset, cy + offset), (cx - offset, cy - offset), dxfattribs={'layer': 'GEOMETRIA'})
        
        # Sarok számozás
        msp.add_text(f"S{i+1}", dxfattribs={
            'layer': 'SZOVEG',
            'height': 3,
            'insert': (cx - 5, cy + 15)
        })
    
    # Koordináta tengelyek
    axis_length = 150
    # X tengely
    msp.add_line((-20, 0), (axis_length, 0), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((axis_length - 5, -3), (axis_length, 0), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((axis_length - 5, 3), (axis_length, 0), dxfattribs={'layer': 'VONALAK'})
    msp.add_text("X", dxfattribs={'layer': 'SZOVEG', 'height': 6, 'insert': (axis_length + 5, -3)})
    
    # Y tengely
    msp.add_line((0, -20), (0, axis_length), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((-3, axis_length - 5), (0, axis_length), dxfattribs={'layer': 'VONALAK'})
    msp.add_line((3, axis_length - 5), (0, axis_length), dxfattribs={'layer': 'VONALAK'})
    msp.add_text("Y", dxfattribs={'layer': 'SZOVEG', 'height': 6, 'insert': (-8, axis_length + 5)})
    
    # Fájl mentése
    doc.saveas('test_drawing.dxf')
    print("Teszt DXF fájl létrehozva: test_drawing.dxf")


if __name__ == "__main__":
    try:
        create_test_dxf()
    except Exception as e:
        print(f"Hiba a teszt DXF fájl létrehozásakor: {e}")
        print("Győződjön meg róla, hogy az ezdxf csomag telepítve van: pip install ezdxf")
