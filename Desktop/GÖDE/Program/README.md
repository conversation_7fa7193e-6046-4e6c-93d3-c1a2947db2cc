# DXF Viewer

AutoCAD-szerű DXF fájl megjelenítő alkalmazás Python tkinter-rel.

## Funkciók

### Alapvető funkciók
- DXF fájlok megnyitása és megjelenítése
- Végtelen canvas AutoCAD-szer<PERSON> navigációval
- Zoom in/out e<PERSON>rk<PERSON><PERSON><PERSON><PERSON> vagy bill<PERSON>k<PERSON>
- Pan (mozgatás) egér húz<PERSON>sal
- Teljes nézet funkció

### Támogatott DXF elemek
- Vonalak (LINE)
- <PERSON><PERSON><PERSON><PERSON><PERSON> (CIRCLE)
- Ívek (ARC)
- Polyline-ok (POLYLINE, LWPOLYLINE)
- Szövegek (TEXT)

### Réteg kezelés
- Rétegek listázása
- Rétegek be/ki kapcsolása
- Összes réteg be/ki kapcsolása
- Rétegenkénti entitás számláló

### Panel kezelés (ÚJ!)
- Automatikus z<PERSON> al<PERSON> (panel) felismerés
- Panel kiválasztási mód
- Aktív panel kiemelése
- Panel információk megjelenítése (terület, kerület)

### Szabványlapok kezelése (ÚJ!)
- Szabványos gipszkarton lapok hozzáadása
- Több fajta szabványlap típus támogatása
- Lapok darabszámának megadása
- Vizuális megjelenítés a canvas-on
- Szabadon mozgatható lapok (drag & drop)
- Automatikus rács elrendezés

### Navigáció
- **Egérkerék**: Zoom in/out az egér pozíciója körül
- **Bal egérgomb húzás**: Pan (mozgatás)
- **+ / - billentyűk**: Zoom in/out a canvas közepén
- **Home billentyű**: Teljes nézet (fit to window)

## Telepítés

### Követelmények
- Python 3.7 vagy újabb
- tkinter (általában a Python-nal együtt települ)

### Függőségek telepítése
```bash
pip install -r requirements.txt
```

vagy

```bash
pip install ezdxf
```

## Használat

### Alkalmazás indítása
```bash
python dxf_viewer.py
```

### DXF fájl megnyitása
1. Kattintson a "Megnyitás" gombra az eszköztáron
2. Vagy használja a Fájl > Megnyitás menüt
3. Vagy nyomja meg a Ctrl+O billentyűkombinációt

### Navigáció
- **Zoom**: Egérkerék vagy +/- billentyűk
- **Pan**: Bal egérgomb lenyomva tartása és húzás
- **Teljes nézet**: "Teljes nézet" gomb vagy Home billentyű

### Rétegek kezelése
- Dupla kattintás egy rétegen: be/ki kapcsolás
- "Összes be" gomb: minden réteg megjelenítése
- "Összes ki" gomb: minden réteg elrejtése

### Panel kiválasztás (ÚJ!)
- "Panel kiválasztása" gomb: kiválasztási mód be/ki kapcsolása
- Kiválasztási módban: kattintás egy panelre kiválasztja azt
- Zöld szaggatott vonalak: elérhető panelek (eredeti alakzat kontúrja)
- Piros vastag vonalak: aktív (kiválasztott) panel
- Panel ID megjelenítése fekete háttérrel
- Állapotsorban: panel információk (ID, terület)

### Szabványlapok kezelése (ÚJ!)
- "Új típus" gomb: új szabványlap típus létrehozása
- "Lapok hozzáadása" gomb: szabványlapok hozzáadása a canvas-hoz
- Szabadon mozgatható lapok: húzás bal egérgombbal
- Automatikus elrendezés: a DXF rajz mellett, rács formában
- Színes megjelenítés: minden típusnak saját színe
- Lap információk: név, méretek, ID megjelenítése

## Fájl struktúra

```
DXF_Viewer/
├── dxf_viewer.py          # Fő alkalmazás
├── dxf_parser.py          # DXF fájl parser
├── canvas_manager.py      # Canvas kezelő (zoom, pan)
├── geometry_renderer.py   # Geometria renderelő
├── requirements.txt       # Python függőségek
├── README.md             # Ez a fájl
└── test_files/           # Teszt DXF fájlok (opcionális)
```

## Fejlesztői információk

### Architektúra
Az alkalmazás moduláris felépítésű:

1. **DXFParser**: DXF fájlok beolvasása és értelmezése
2. **CanvasManager**: Végtelen canvas, zoom és pan funkcionalitás
3. **GeometryRenderer**: DXF elemek rajzolása a canvas-ra
4. **DXFViewer**: Fő alkalmazás és UI kezelés

### Koordináta rendszerek
- **Világkoordináták**: DXF fájlban tárolt eredeti koordináták
- **Canvas koordináták**: Tkinter canvas koordináták (pixel)
- **Transzformáció**: Világkoordináták ↔ Canvas koordináták

### Teljesítmény optimalizálás
- Csak a látható elemek rajzolása
- Bounding box alapú culling
- Adaptív rács megjelenítés

## Ismert korlátozások

- Csak 2D DXF elemek támogatottak
- Komplex polyline-ok (ívekkel) egyszerűsítve jelennek meg
- Blokkok és insert-ek nem támogatottak
- Hatch-ek nem támogatottak
- Dimension-ök nem támogatottak

## Hibaelhárítás

### "ModuleNotFoundError: No module named 'ezdxf'"
```bash
pip install ezdxf
```

### "Nem sikerült betölteni a DXF fájlt"
- Ellenőrizze, hogy a fájl valóban DXF formátumú
- Próbáljon egy egyszerűbb DXF fájlt
- Ellenőrizze a fájl jogosultságokat

### Lassú megjelenítés nagy fájloknál
- A program optimalizált, de nagyon nagy fájlok esetén lassulás várható
- Próbálja ki a rétegek ki/be kapcsolását

## Licenc

MIT License - lásd a forráskódban.

## Közreműködés

A projekt nyílt forráskódú, közreműködés szívesen fogadott!
