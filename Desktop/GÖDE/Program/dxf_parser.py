"""
DXF fájl parser modul
Kezeli a DXF fájlok beolvasását és a geometriai elemek kinyerését
"""

import ezdxf
from typing import List, Dict, Tuple, Any
import math


class DXFParser:
    def __init__(self):
        self.doc = None
        self.entities = []
        self.layers = {}
        self.bounds = None
    
    def load_dxf(self, filepath: str) -> bool:
        """DXF fájl betöltése"""
        try:
            self.doc = ezdxf.readfile(filepath)
            self.entities = []
            self.layers = {}
            self._parse_entities()
            self._calculate_bounds()
            return True
        except Exception as e:
            print(f"Hiba a DXF fájl betöltésekor: {e}")
            return False
    
    def _parse_entities(self):
        """Entitások feldolgozása"""
        if not self.doc:
            return
        
        modelspace = self.doc.modelspace()
        
        for entity in modelspace:
            parsed_entity = self._parse_entity(entity)
            if parsed_entity:
                self.entities.append(parsed_entity)
                
                # Réteg információ tárolása
                layer_name = entity.dxf.layer
                if layer_name not in self.layers:
                    self.layers[layer_name] = {
                        'visible': True,
                        'color': getattr(entity.dxf, 'color', 7),  # 7 = fehér alapértelmezett
                        'entities': []
                    }
                self.layers[layer_name]['entities'].append(parsed_entity)
    
    def _parse_entity(self, entity) -> Dict[str, Any]:
        """Egyetlen entitás feldolgozása"""
        entity_type = entity.dxftype()
        
        if entity_type == 'LINE':
            return {
                'type': 'line',
                'start': (entity.dxf.start.x, entity.dxf.start.y),
                'end': (entity.dxf.end.x, entity.dxf.end.y),
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
        
        elif entity_type == 'CIRCLE':
            return {
                'type': 'circle',
                'center': (entity.dxf.center.x, entity.dxf.center.y),
                'radius': entity.dxf.radius,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
        
        elif entity_type == 'ARC':
            return {
                'type': 'arc',
                'center': (entity.dxf.center.x, entity.dxf.center.y),
                'radius': entity.dxf.radius,
                'start_angle': math.radians(entity.dxf.start_angle),
                'end_angle': math.radians(entity.dxf.end_angle),
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
        
        elif entity_type == 'POLYLINE' or entity_type == 'LWPOLYLINE':
            points = []
            if hasattr(entity, 'vertices'):
                for vertex in entity.vertices:
                    points.append((vertex.dxf.location.x, vertex.dxf.location.y))
            elif hasattr(entity, 'get_points'):
                for point in entity.get_points():
                    points.append((point[0], point[1]))
            
            return {
                'type': 'polyline',
                'points': points,
                'closed': entity.is_closed if hasattr(entity, 'is_closed') else False,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
        
        elif entity_type == 'TEXT':
            return {
                'type': 'text',
                'position': (entity.dxf.insert.x, entity.dxf.insert.y),
                'text': entity.dxf.text,
                'height': entity.dxf.height,
                'layer': entity.dxf.layer,
                'color': getattr(entity.dxf, 'color', 7)
            }
        
        return None
    
    def _calculate_bounds(self):
        """Rajz határainak kiszámítása"""
        if not self.entities:
            self.bounds = (0, 0, 100, 100)  # alapértelmezett
            return
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for entity in self.entities:
            if entity['type'] == 'line':
                min_x = min(min_x, entity['start'][0], entity['end'][0])
                max_x = max(max_x, entity['start'][0], entity['end'][0])
                min_y = min(min_y, entity['start'][1], entity['end'][1])
                max_y = max(max_y, entity['start'][1], entity['end'][1])
            
            elif entity['type'] == 'circle':
                cx, cy = entity['center']
                r = entity['radius']
                min_x = min(min_x, cx - r)
                max_x = max(max_x, cx + r)
                min_y = min(min_y, cy - r)
                max_y = max(max_y, cy + r)
            
            elif entity['type'] == 'arc':
                cx, cy = entity['center']
                r = entity['radius']
                min_x = min(min_x, cx - r)
                max_x = max(max_x, cx + r)
                min_y = min(min_y, cy - r)
                max_y = max(max_y, cy + r)
            
            elif entity['type'] == 'polyline':
                for point in entity['points']:
                    min_x = min(min_x, point[0])
                    max_x = max(max_x, point[0])
                    min_y = min(min_y, point[1])
                    max_y = max(max_y, point[1])
            
            elif entity['type'] == 'text':
                min_x = min(min_x, entity['position'][0])
                max_x = max(max_x, entity['position'][0])
                min_y = min(min_y, entity['position'][1])
                max_y = max(max_y, entity['position'][1])
        
        # Kis margó hozzáadása
        margin = max((max_x - min_x), (max_y - min_y)) * 0.1
        self.bounds = (min_x - margin, min_y - margin, max_x + margin, max_y + margin)
    
    def get_entities(self) -> List[Dict[str, Any]]:
        """Entitások listájának visszaadása"""
        return self.entities
    
    def get_layers(self) -> Dict[str, Dict]:
        """Rétegek információjának visszaadása"""
        return self.layers
    
    def get_bounds(self) -> Tuple[float, float, float, float]:
        """Rajz határainak visszaadása (min_x, min_y, max_x, max_y)"""
        return self.bounds if self.bounds else (0, 0, 100, 100)
