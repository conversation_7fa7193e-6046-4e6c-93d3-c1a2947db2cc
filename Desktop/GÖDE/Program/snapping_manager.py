"""
Snapping kezelő modul
Kezeli a szabványlapok és panelek közötti snapping funkcionalitást
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class SnapType(Enum):
    """Snap típusok"""
    CORNER = "corner"
    EDGE_HORIZONTAL = "edge_horizontal"
    EDGE_VERTICAL = "edge_vertical"


class GripType(Enum):
    """Fogási pontok típusai"""
    CORNER_TL = "corner_top_left"
    CORNER_TR = "corner_top_right"
    CORNER_BL = "corner_bottom_left"
    CORNER_BR = "corner_bottom_right"
    EDGE_TOP = "edge_top"
    EDGE_BOTTOM = "edge_bottom"
    EDGE_LEFT = "edge_left"
    EDGE_RIGHT = "edge_right"
    CENTER = "center"


@dataclass
class SnapPoint:
    """Snap pont adatstruktúra"""
    x: float
    y: float
    snap_type: SnapType
    source_id: str  # Melyik objektumhoz tartozik
    source_type: str  # "sheet", "panel", "placed_piece"


@dataclass
class GripPoint:
    """Fogási pont adatstruktúra"""
    x: float
    y: float
    grip_type: GripType
    cursor: str  # Kurzor típus


class SnappingManager:
    """Snapping kezelő osztály"""
    
    def __init__(self):
        self.snap_distance = 20.0  # Snap távolság pixelben
        self.snap_enabled = True
        self.show_snap_guides = True
        self.snap_points: List[SnapPoint] = []
        
        # Kurzor típusok a különböző grip pontokhoz
        self.grip_cursors = {
            GripType.CORNER_TL: "top_left_corner",
            GripType.CORNER_TR: "top_right_corner", 
            GripType.CORNER_BL: "bottom_left_corner",
            GripType.CORNER_BR: "bottom_right_corner",
            GripType.EDGE_TOP: "top_side",
            GripType.EDGE_BOTTOM: "bottom_side",
            GripType.EDGE_LEFT: "left_side",
            GripType.EDGE_RIGHT: "right_side",
            GripType.CENTER: "fleur"
        }
    
    def set_snap_enabled(self, enabled: bool):
        """Snapping be/kikapcsolása"""
        self.snap_enabled = enabled
    
    def is_snap_enabled(self) -> bool:
        """Snapping állapot lekérdezése"""
        return self.snap_enabled
    
    def update_snap_points(self, sheets: List, panels: List, placed_pieces: List = None):
        """Snap pontok frissítése az összes objektumból"""
        self.snap_points = []
        
        # Szabványlapok snap pontjai
        for sheet in sheets:
            self._add_sheet_snap_points(sheet)
        
        # Panelek snap pontjai
        for panel in panels:
            self._add_panel_snap_points(panel)
        
        # Elhelyezett darabok snap pontjai
        if placed_pieces:
            for piece in placed_pieces:
                self._add_placed_piece_snap_points(piece)
    
    def _add_sheet_snap_points(self, sheet):
        """Szabványlap snap pontjainak hozzáadása"""
        x, y = sheet.position
        width, height = sheet.width, sheet.height
        
        # Sarkok
        corners = [
            (x, y, SnapType.CORNER),
            (x + width, y, SnapType.CORNER),
            (x, y + height, SnapType.CORNER),
            (x + width, y + height, SnapType.CORNER)
        ]
        
        for cx, cy, snap_type in corners:
            self.snap_points.append(SnapPoint(cx, cy, snap_type, sheet.id, "sheet"))
        
        # Élek
        # Vízszintes élek
        self.snap_points.append(SnapPoint(x, y, SnapType.EDGE_HORIZONTAL, sheet.id, "sheet"))
        self.snap_points.append(SnapPoint(x, y + height, SnapType.EDGE_HORIZONTAL, sheet.id, "sheet"))
        
        # Függőleges élek
        self.snap_points.append(SnapPoint(x, y, SnapType.EDGE_VERTICAL, sheet.id, "sheet"))
        self.snap_points.append(SnapPoint(x + width, y, SnapType.EDGE_VERTICAL, sheet.id, "sheet"))
    
    def _add_panel_snap_points(self, panel):
        """Panel snap pontjainak hozzáadása"""
        min_x, min_y, max_x, max_y = panel.bounds
        
        # Sarkok
        corners = [
            (min_x, min_y, SnapType.CORNER),
            (max_x, min_y, SnapType.CORNER),
            (min_x, max_y, SnapType.CORNER),
            (max_x, max_y, SnapType.CORNER)
        ]
        
        for cx, cy, snap_type in corners:
            self.snap_points.append(SnapPoint(cx, cy, snap_type, str(panel.id), "panel"))
        
        # Élek (egyszerűsített - csak a bounding box élei)
        self.snap_points.append(SnapPoint(min_x, min_y, SnapType.EDGE_HORIZONTAL, str(panel.id), "panel"))
        self.snap_points.append(SnapPoint(min_x, max_y, SnapType.EDGE_HORIZONTAL, str(panel.id), "panel"))
        self.snap_points.append(SnapPoint(min_x, min_y, SnapType.EDGE_VERTICAL, str(panel.id), "panel"))
        self.snap_points.append(SnapPoint(max_x, min_y, SnapType.EDGE_VERTICAL, str(panel.id), "panel"))
    
    def _add_placed_piece_snap_points(self, piece):
        """Elhelyezett darab snap pontjainak hozzáadása"""
        # Hasonló a szabványlapokhoz
        x, y = piece.position
        width, height = piece.width, piece.height
        
        # Sarkok
        corners = [
            (x, y, SnapType.CORNER),
            (x + width, y, SnapType.CORNER),
            (x, y + height, SnapType.CORNER),
            (x + width, y + height, SnapType.CORNER)
        ]
        
        for cx, cy, snap_type in corners:
            self.snap_points.append(SnapPoint(cx, cy, snap_type, piece.id, "placed_piece"))
    
    def get_grip_points(self, sheet) -> List[GripPoint]:
        """Szabványlap grip pontjainak visszaadása"""
        x, y = sheet.position
        width, height = sheet.width, sheet.height
        
        grip_points = [
            # Sarkok
            GripPoint(x, y, GripType.CORNER_TL, self.grip_cursors[GripType.CORNER_TL]),
            GripPoint(x + width, y, GripType.CORNER_TR, self.grip_cursors[GripType.CORNER_TR]),
            GripPoint(x, y + height, GripType.CORNER_BL, self.grip_cursors[GripType.CORNER_BL]),
            GripPoint(x + width, y + height, GripType.CORNER_BR, self.grip_cursors[GripType.CORNER_BR]),
            
            # Élek közepei
            GripPoint(x + width/2, y, GripType.EDGE_TOP, self.grip_cursors[GripType.EDGE_TOP]),
            GripPoint(x + width/2, y + height, GripType.EDGE_BOTTOM, self.grip_cursors[GripType.EDGE_BOTTOM]),
            GripPoint(x, y + height/2, GripType.EDGE_LEFT, self.grip_cursors[GripType.EDGE_LEFT]),
            GripPoint(x + width, y + height/2, GripType.EDGE_RIGHT, self.grip_cursors[GripType.EDGE_RIGHT]),
            
            # Középpont
            GripPoint(x + width/2, y + height/2, GripType.CENTER, self.grip_cursors[GripType.CENTER])
        ]
        
        return grip_points
    
    def find_closest_grip(self, sheet, mouse_x: float, mouse_y: float, 
                         canvas_manager) -> Optional[GripPoint]:
        """Legközelebbi grip pont keresése"""
        grip_points = self.get_grip_points(sheet)
        closest_grip = None
        min_distance = float('inf')
        
        for grip in grip_points:
            # Világkoordináták canvas koordinátákra
            canvas_x, canvas_y = canvas_manager.world_to_canvas(grip.x, grip.y)
            
            # Távolság számítása
            dx = mouse_x - canvas_x
            dy = mouse_y - canvas_y
            distance = math.sqrt(dx * dx + dy * dy)
            
            # Grip terület (15 pixel)
            if distance < 15 and distance < min_distance:
                min_distance = distance
                closest_grip = grip
        
        return closest_grip
    
    def find_snap_target(self, x: float, y: float, grip_type: GripType, 
                        exclude_id: str = None) -> Optional[SnapPoint]:
        """Snap cél keresése"""
        if not self.snap_enabled:
            return None
        
        closest_snap = None
        min_distance = float('inf')
        
        for snap_point in self.snap_points:
            # Kihagyjuk a saját objektumot
            if snap_point.source_id == exclude_id:
                continue
            
            # Ellenőrizzük a kompatibilitást
            if not self._is_compatible_snap(grip_type, snap_point.snap_type):
                continue
            
            # Távolság számítása
            dx = x - snap_point.x
            dy = y - snap_point.y
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance < self.snap_distance and distance < min_distance:
                min_distance = distance
                closest_snap = snap_point
        
        return closest_snap
    
    def _is_compatible_snap(self, grip_type: GripType, snap_type: SnapType) -> bool:
        """Ellenőrzi, hogy a grip és snap típusok kompatibilisek-e"""
        # Sarkok mindig snap-elhetnek sarokhoz
        corner_grips = {GripType.CORNER_TL, GripType.CORNER_TR, 
                       GripType.CORNER_BL, GripType.CORNER_BR}
        
        if grip_type in corner_grips and snap_type == SnapType.CORNER:
            return True
        
        # Vízszintes élek vízszintes élekhez
        horizontal_grips = {GripType.EDGE_TOP, GripType.EDGE_BOTTOM}
        if grip_type in horizontal_grips and snap_type == SnapType.EDGE_HORIZONTAL:
            return True
        
        # Függőleges élek függőleges élekhez
        vertical_grips = {GripType.EDGE_LEFT, GripType.EDGE_RIGHT}
        if grip_type in vertical_grips and snap_type == SnapType.EDGE_VERTICAL:
            return True
        
        return False
    
    def calculate_snap_position(self, sheet, grip_type: GripType, 
                               snap_point: SnapPoint) -> Tuple[float, float]:
        """Snap pozíció kiszámítása"""
        width, height = sheet.width, sheet.height
        
        # Grip pont relatív pozíciója a lap bal felső sarkához képest
        grip_offsets = {
            GripType.CORNER_TL: (0, 0),
            GripType.CORNER_TR: (width, 0),
            GripType.CORNER_BL: (0, height),
            GripType.CORNER_BR: (width, height),
            GripType.EDGE_TOP: (width/2, 0),
            GripType.EDGE_BOTTOM: (width/2, height),
            GripType.EDGE_LEFT: (0, height/2),
            GripType.EDGE_RIGHT: (width, height/2),
            GripType.CENTER: (width/2, height/2)
        }
        
        offset_x, offset_y = grip_offsets[grip_type]
        
        # Új pozíció = snap pont - grip offset
        new_x = snap_point.x - offset_x
        new_y = snap_point.y - offset_y
        
        return (new_x, new_y)
    
    def get_snap_guides(self, active_snap: SnapPoint = None) -> List[Dict]:
        """Snap segédvonalak visszaadása"""
        if not self.show_snap_guides or not active_snap:
            return []
        
        guides = []
        
        # Vízszintes segédvonal
        if active_snap.snap_type in [SnapType.CORNER, SnapType.EDGE_HORIZONTAL]:
            guides.append({
                'type': 'horizontal',
                'y': active_snap.y,
                'color': '#00FF00',
                'style': 'dashed'
            })
        
        # Függőleges segédvonal
        if active_snap.snap_type in [SnapType.CORNER, SnapType.EDGE_VERTICAL]:
            guides.append({
                'type': 'vertical',
                'x': active_snap.x,
                'color': '#00FF00',
                'style': 'dashed'
            })
        
        return guides
