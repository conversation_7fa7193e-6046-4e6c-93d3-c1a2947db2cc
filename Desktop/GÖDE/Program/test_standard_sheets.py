"""
Szabványlapok tesztelése
"""

from standard_sheet_manager import StandardSheetManager


def test_standard_sheets():
    """Szabványlapok kezelésének tesztelése"""
    
    manager = StandardSheetManager()
    
    print("Alapértelmezett szabványlap típusok:")
    for name, info in manager.get_sheet_types().items():
        print(f"  {name}: {info['width']}x{info['height']} - {info['color']}")
    
    print("\nÚj típus hozzáadása...")
    manager.add_sheet_type("Teszt lap", 800, 1200, "#FF0000")
    
    print("\nLapok hozzáadása...")
    manager.add_sheets("GKB 1200x2000", 3)
    manager.add_sheets("Teszt lap", 2)
    
    print(f"\nÖsszes lap: {len(manager.get_sheets())}")
    
    for sheet in manager.get_sheets():
        print(f"  {sheet.name} - {sheet.width}x{sheet.height} - Pozíció: {sheet.position}")
    
    print("\nPozíció beállítása...")
    manager.set_sheet_area_position((0, 0, 1000, 1000))
    
    print("Lapok új pozíciói:")
    for sheet in manager.get_sheets():
        print(f"  {sheet.name} - Pozíció: {sheet.position}")


if __name__ == "__main__":
    test_standard_sheets()
