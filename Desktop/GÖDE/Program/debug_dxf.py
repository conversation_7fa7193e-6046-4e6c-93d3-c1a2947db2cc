"""
Debug DXF betöltés - részletes információk
"""

from dxf_parser import DXFParser
import sys


def debug_dxf_loading(filepath):
    """DXF betöltés debug információkkal"""
    parser = DXFParser()
    
    print(f"DXF fájl betöltése: {filepath}")
    print("-" * 50)
    
    success = parser.load_dxf(filepath)
    
    if not success:
        print("HIBA: Nem sikerült betölteni a DXF fájlt!")
        return
    
    entities = parser.get_entities()
    layers = parser.get_layers()
    bounds = parser.get_bounds()
    
    print(f"Betöltött entitások száma: {len(entities)}")
    print(f"Rétegek száma: {len(layers)}")
    print(f"Rajz határai: {bounds}")
    print()
    
    # Első 10 entitás részletei
    print("ELSŐ 10 ENTITÁS RÉSZLETEI:")
    for i, entity in enumerate(entities[:10]):
        print(f"\n{i+1}. {entity['type'].upper()}:")
        print(f"   Réteg: {entity['layer']}")
        print(f"   Szín: {entity['color']}")
        
        if entity['type'] == 'line':
            print(f"   Start: {entity['start']}")
            print(f"   End: {entity['end']}")
            # Vonal hossza
            dx = entity['end'][0] - entity['start'][0]
            dy = entity['end'][1] - entity['start'][1]
            length = (dx*dx + dy*dy)**0.5
            print(f"   Hossz: {length:.6f}")
    
    # Réteg információk
    print("\nRÉTEG INFORMÁCIÓK:")
    for layer_name, layer_info in layers.items():
        print(f"\n{layer_name}:")
        print(f"   Látható: {layer_info['visible']}")
        print(f"   Szín: {layer_info['color']}")
        print(f"   Entitások: {len(layer_info['entities'])}")
    
    # Koordináta tartományok
    print("\nKOORDINÁTA TARTOMÁNYOK:")
    if entities:
        all_x = []
        all_y = []
        
        for entity in entities:
            if entity['type'] == 'line':
                all_x.extend([entity['start'][0], entity['end'][0]])
                all_y.extend([entity['start'][1], entity['end'][1]])
        
        if all_x and all_y:
            print(f"X tartomány: {min(all_x):.6f} - {max(all_x):.6f}")
            print(f"Y tartomány: {min(all_y):.6f} - {max(all_y):.6f}")
            print(f"X szélesség: {max(all_x) - min(all_x):.6f}")
            print(f"Y magasság: {max(all_y) - min(all_y):.6f}")
    
    # Nulla közeli koordináták ellenőrzése
    print("\nNULLA KÖZELI KOORDINÁTÁK ELLENŐRZÉSE:")
    zero_threshold = 1e-10
    zero_coords = 0
    
    for entity in entities:
        if entity['type'] == 'line':
            coords = [entity['start'][0], entity['start'][1], entity['end'][0], entity['end'][1]]
            for coord in coords:
                if abs(coord) < zero_threshold:
                    zero_coords += 1
    
    print(f"Nulla közeli koordináták száma (< {zero_threshold}): {zero_coords}")
    
    return parser


if __name__ == "__main__":
    if len(sys.argv) > 1:
        filepath = sys.argv[1]
    else:
        filepath = "GODEFEHER.dxf"
    
    debug_dxf_loading(filepath)
