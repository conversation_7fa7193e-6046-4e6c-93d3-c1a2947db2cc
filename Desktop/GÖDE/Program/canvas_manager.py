"""
Canvas kezelő modul
Ke<PERSON>i a végtelen canvas funkcionalitást, zoom és pan műveleteket
"""

import tkinter as tk
from tkinter import ttk
import math
from typing import Tuple, Optional


class CanvasManager:
    def __init__(self, canvas: tk.Canvas):
        self.canvas = canvas
        self.scale = 1.0
        self.offset_x = 0.0
        self.offset_y = 0.0
        
        # <PERSON><PERSON>r <PERSON> követése
        self.last_x = 0
        self.last_y = 0
        self.dragging = False
        
        # Zoom beállítások
        self.min_scale = 0.001
        self.max_scale = 1000.0
        self.zoom_factor = 1.2
        
        # Eseménykezelők beállítása
        self._setup_bindings()
    
    def _setup_bindings(self):
        """Egér és billentyűzet eseménykezelők beállítása"""
        # Zoom egérkerékkel
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.canvas.bind("<Button-4>", self._on_mousewheel)  # Linux
        self.canvas.bind("<Button-5>", self._on_mousewheel)  # Linux
        
        # Pan húzással
        self.canvas.bind("<Button-1>", self._on_button_press)
        self.canvas.bind("<B1-Motion>", self._on_drag)
        self.canvas.bind("<ButtonRelease-1>", self._on_button_release)
        
        # Jobb egérgomb menü (később)
        self.canvas.bind("<Button-3>", self._on_right_click)
        
        # Billentyűzet események
        self.canvas.bind("<Key>", self._on_key_press)
        self.canvas.focus_set()
    
    def _on_mousewheel(self, event):
        """Egérkerék eseménykezelő - zoom"""
        # Zoom irány meghatározása
        if event.delta > 0 or event.num == 4:
            zoom_in = True
        else:
            zoom_in = False
        
        # Egér pozíció a canvas-on
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # Zoom végrehajtása az egér pozíciója körül
        self.zoom_at_point(canvas_x, canvas_y, zoom_in)
    
    def _on_button_press(self, event):
        """Egérgomb lenyomás - pan kezdete"""
        self.last_x = event.x
        self.last_y = event.y
        self.dragging = True
        # Ne változtassuk a kurzort, ha már be van állítva (pl. szabványlap húzás)
        if self.canvas.cget("cursor") == "":
            self.canvas.config(cursor="fleur")
    
    def _on_drag(self, event):
        """Egér húzás - pan végrehajtása"""
        if self.dragging:
            dx = event.x - self.last_x
            dy = event.y - self.last_y
            
            self.pan(dx, dy)
            
            self.last_x = event.x
            self.last_y = event.y
    
    def _on_button_release(self, event):
        """Egérgomb felengedés - pan vége"""
        self.dragging = False
        self.canvas.config(cursor="")
    
    def _on_right_click(self, event):
        """Jobb egérgomb - kontextus menü"""
        # Később implementálható kontextus menü
        pass
    
    def _on_key_press(self, event):
        """Billentyűzet eseménykezelő"""
        if event.keysym == "plus" or event.keysym == "equal":
            self.zoom_in()
        elif event.keysym == "minus":
            self.zoom_out()
        elif event.keysym == "Home":
            self.fit_to_window()
    
    def zoom_at_point(self, canvas_x: float, canvas_y: float, zoom_in: bool):
        """Zoom végrehajtása egy adott pont körül"""
        old_scale = self.scale
        
        if zoom_in:
            new_scale = min(self.scale * self.zoom_factor, self.max_scale)
        else:
            new_scale = max(self.scale / self.zoom_factor, self.min_scale)
        
        if new_scale == old_scale:
            return  # Nem változott a zoom
        
        # Világkoordináták kiszámítása az egér pozíciójánál
        world_x = (canvas_x - self.offset_x) / old_scale
        world_y = (canvas_y - self.offset_y) / old_scale
        
        # Új offset kiszámítása
        self.scale = new_scale
        self.offset_x = canvas_x - world_x * new_scale
        self.offset_y = canvas_y - world_y * new_scale
        
        # Canvas frissítése
        self._update_canvas()
    
    def zoom_in(self):
        """Zoom in a canvas közepén"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        center_x = canvas_width / 2
        center_y = canvas_height / 2
        self.zoom_at_point(center_x, center_y, True)
    
    def zoom_out(self):
        """Zoom out a canvas közepén"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        center_x = canvas_width / 2
        center_y = canvas_height / 2
        self.zoom_at_point(center_x, center_y, False)
    
    def pan(self, dx: float, dy: float):
        """Pan végrehajtása"""
        self.offset_x += dx
        self.offset_y += dy
        self._update_canvas()
    
    def fit_to_window(self, bounds: Optional[Tuple[float, float, float, float]] = None):
        """Rajz illesztése az ablakhoz"""
        if not bounds:
            return
        
        min_x, min_y, max_x, max_y = bounds
        
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            return
        
        # Rajz méretei
        drawing_width = max_x - min_x
        drawing_height = max_y - min_y
        
        if drawing_width <= 0 or drawing_height <= 0:
            return
        
        # Skála kiszámítása (90%-os kitöltés)
        scale_x = (canvas_width * 0.9) / drawing_width
        scale_y = (canvas_height * 0.9) / drawing_height
        self.scale = min(scale_x, scale_y)
        
        # Középre igazítás
        drawing_center_x = (min_x + max_x) / 2
        drawing_center_y = (min_y + max_y) / 2
        
        canvas_center_x = canvas_width / 2
        canvas_center_y = canvas_height / 2
        
        self.offset_x = canvas_center_x - drawing_center_x * self.scale
        self.offset_y = canvas_center_y - drawing_center_y * self.scale
        
        self._update_canvas()
    
    def world_to_canvas(self, world_x: float, world_y: float) -> Tuple[float, float]:
        """Világkoordináták átalakítása canvas koordinátákra"""
        canvas_x = world_x * self.scale + self.offset_x
        canvas_y = world_y * self.scale + self.offset_y
        return canvas_x, canvas_y
    
    def canvas_to_world(self, canvas_x: float, canvas_y: float) -> Tuple[float, float]:
        """Canvas koordináták átalakítása világkoordinátákra"""
        world_x = (canvas_x - self.offset_x) / self.scale
        world_y = (canvas_y - self.offset_y) / self.scale
        return world_x, world_y
    
    def get_visible_bounds(self) -> Tuple[float, float, float, float]:
        """Látható terület határainak visszaadása világkoordinátákban"""
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        min_world_x, min_world_y = self.canvas_to_world(0, 0)
        max_world_x, max_world_y = self.canvas_to_world(canvas_width, canvas_height)
        
        return min_world_x, min_world_y, max_world_x, max_world_y
    
    def _update_canvas(self):
        """Canvas frissítése - ezt a renderelő fogja használni"""
        # Ez egy callback lesz a renderelő számára
        if hasattr(self, 'update_callback') and self.update_callback:
            self.update_callback()
    
    def set_update_callback(self, callback):
        """Frissítési callback beállítása"""
        self.update_callback = callback
    
    def get_transform_info(self) -> dict:
        """Transzformációs információk visszaadása"""
        return {
            'scale': self.scale,
            'offset_x': self.offset_x,
            'offset_y': self.offset_y
        }
