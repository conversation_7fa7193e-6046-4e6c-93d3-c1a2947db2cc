"""
Puzzle kezelő modul
Kezeli a panelek kirakatását alapanyagokból és a maradékok kezelését
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import uuid
from material_panel import MaterialPiece


@dataclass
class PlacedPiece:
    """Elhelyezett darab adatstruktúra"""
    id: str
    material_id: str
    position: Tuple[float, float]
    width: float
    height: float
    color: str
    rotation: float = 0.0  # Forgatás (későbbi bővítéshez)


@dataclass
class CutInstruction:
    """Vágási utasítás adatstruktúra"""
    material_name: str
    cuts: List[Tuple[float, float, float, float]]  # (x1, y1, x2, y2)
    resulting_pieces: List[Dict[str, Any]]


class PuzzleManager:
    """Puzzle kezelő osztály"""
    
    def __init__(self):
        self.placed_pieces: List[PlacedPiece] = []
        self.active_panel = None
        self.cut_instructions: List[CutInstruction] = []
    
    def set_active_panel(self, panel):
        """Aktív panel beállítása"""
        self.active_panel = panel
        # Elhelyezett darabok törlése új panel kiválasztásakor
        self.placed_pieces = []
    
    def place_material(self, material: MaterialPiece, position: Tuple[float, float]) -> PlacedPiece:
        """Alapanyag elhelyezése a canvas-on"""
        placed_piece = PlacedPiece(
            id=str(uuid.uuid4()),
            material_id=material.id,
            position=position,
            width=material.width,
            height=material.height,
            color=material.color
        )
        
        self.placed_pieces.append(placed_piece)
        return placed_piece
    
    def remove_placed_piece(self, piece_id: str):
        """Elhelyezett darab eltávolítása"""
        self.placed_pieces = [p for p in self.placed_pieces if p.id != piece_id]
    
    def get_placed_pieces(self) -> List[PlacedPiece]:
        """Elhelyezett darabok visszaadása"""
        return self.placed_pieces.copy()
    
    def process_panel_cutting(self, material_panel) -> List[MaterialPiece]:
        """Panel kivágás feldolgozása - Enter billentyű lenyomásakor"""
        if not self.active_panel or not self.placed_pieces:
            return []
        
        remnants = []
        panel_bounds = self.active_panel.bounds
        
        for placed_piece in self.placed_pieces:
            # Ellenőrizzük, hogy az elhelyezett darab hogyan viszonyul a panelhez
            intersection = self._calculate_intersection(placed_piece, panel_bounds)
            
            if intersection:
                # Van átfedés - számítsuk ki a maradékokat
                piece_remnants = self._calculate_remnants(placed_piece, intersection)
                
                for remnant_data in piece_remnants:
                    # Maradék darab létrehozása
                    remnant = MaterialPiece(
                        id=str(uuid.uuid4()),
                        name=f"Maradék {len(remnants) + 1}",
                        width=remnant_data['width'],
                        height=remnant_data['height'],
                        color=placed_piece.color,
                        material_type="remnant",
                        original_sheet_id=placed_piece.material_id
                    )
                    remnants.append(remnant)
                
                # Vágási utasítás létrehozása
                self._create_cut_instruction(placed_piece, intersection, piece_remnants)
        
        # Elhelyezett darabok törlése a feldolgozás után
        self.placed_pieces = []
        
        return remnants
    
    def _calculate_intersection(self, placed_piece: PlacedPiece, 
                              panel_bounds: Tuple[float, float, float, float]) -> Optional[Dict]:
        """Elhelyezett darab és panel metszéspontjának számítása"""
        # Elhelyezett darab határai
        piece_x, piece_y = placed_piece.position
        piece_bounds = (
            piece_x,
            piece_y,
            piece_x + placed_piece.width,
            piece_y + placed_piece.height
        )
        
        # Panel határai
        panel_min_x, panel_min_y, panel_max_x, panel_max_y = panel_bounds
        
        # Metszéspont számítása
        intersect_min_x = max(piece_bounds[0], panel_min_x)
        intersect_min_y = max(piece_bounds[1], panel_min_y)
        intersect_max_x = min(piece_bounds[2], panel_max_x)
        intersect_max_y = min(piece_bounds[3], panel_max_y)
        
        # Van-e valódi metszéspont?
        if intersect_min_x < intersect_max_x and intersect_min_y < intersect_max_y:
            return {
                'bounds': (intersect_min_x, intersect_min_y, intersect_max_x, intersect_max_y),
                'width': intersect_max_x - intersect_min_x,
                'height': intersect_max_y - intersect_min_y,
                'area': (intersect_max_x - intersect_min_x) * (intersect_max_y - intersect_min_y)
            }
        
        return None
    
    def _calculate_remnants(self, placed_piece: PlacedPiece, 
                          intersection: Dict) -> List[Dict[str, Any]]:
        """Maradék darabok számítása"""
        remnants = []
        
        piece_x, piece_y = placed_piece.position
        piece_width = placed_piece.width
        piece_height = placed_piece.height
        
        intersect_bounds = intersection['bounds']
        intersect_min_x, intersect_min_y, intersect_max_x, intersect_max_y = intersect_bounds
        
        # Bal oldali maradék
        if intersect_min_x > piece_x:
            left_width = intersect_min_x - piece_x
            remnants.append({
                'position': (piece_x, piece_y),
                'width': left_width,
                'height': piece_height,
                'type': 'left'
            })
        
        # Jobb oldali maradék
        if intersect_max_x < piece_x + piece_width:
            right_width = (piece_x + piece_width) - intersect_max_x
            remnants.append({
                'position': (intersect_max_x, piece_y),
                'width': right_width,
                'height': piece_height,
                'type': 'right'
            })
        
        # Felső maradék
        if intersect_min_y > piece_y:
            top_height = intersect_min_y - piece_y
            # Csak a középső részt vesszük (bal és jobb maradék nélkül)
            top_x = max(piece_x, intersect_min_x)
            top_width = min(piece_x + piece_width, intersect_max_x) - top_x
            
            if top_width > 0:
                remnants.append({
                    'position': (top_x, piece_y),
                    'width': top_width,
                    'height': top_height,
                    'type': 'top'
                })
        
        # Alsó maradék
        if intersect_max_y < piece_y + piece_height:
            bottom_height = (piece_y + piece_height) - intersect_max_y
            # Csak a középső részt vesszük (bal és jobb maradék nélkül)
            bottom_x = max(piece_x, intersect_min_x)
            bottom_width = min(piece_x + piece_width, intersect_max_x) - bottom_x
            
            if bottom_width > 0:
                remnants.append({
                    'position': (bottom_x, intersect_max_y),
                    'width': bottom_width,
                    'height': bottom_height,
                    'type': 'bottom'
                })
        
        # Csak a hasznos méretű maradékokat tartjuk meg (min 10x10 mm)
        useful_remnants = []
        for remnant in remnants:
            if remnant['width'] >= 10 and remnant['height'] >= 10:
                useful_remnants.append(remnant)
        
        return useful_remnants
    
    def _create_cut_instruction(self, placed_piece: PlacedPiece, 
                               intersection: Dict, remnants: List[Dict]):
        """Vágási utasítás létrehozása"""
        cuts = []
        
        piece_x, piece_y = placed_piece.position
        intersect_bounds = intersection['bounds']
        intersect_min_x, intersect_min_y, intersect_max_x, intersect_max_y = intersect_bounds
        
        # Vágási vonalak meghatározása
        # Függőleges vágások
        if intersect_min_x > piece_x:
            # Bal oldali vágás
            cuts.append((intersect_min_x, piece_y, intersect_min_x, piece_y + placed_piece.height))
        
        if intersect_max_x < piece_x + placed_piece.width:
            # Jobb oldali vágás
            cuts.append((intersect_max_x, piece_y, intersect_max_x, piece_y + placed_piece.height))
        
        # Vízszintes vágások
        if intersect_min_y > piece_y:
            # Felső vágás
            cuts.append((piece_x, intersect_min_y, piece_x + placed_piece.width, intersect_min_y))
        
        if intersect_max_y < piece_y + placed_piece.height:
            # Alsó vágás
            cuts.append((piece_x, intersect_max_y, piece_x + placed_piece.width, intersect_max_y))
        
        # Vágási utasítás tárolása
        instruction = CutInstruction(
            material_name=f"Anyag {placed_piece.material_id[:8]}",
            cuts=cuts,
            resulting_pieces=remnants
        )
        
        self.cut_instructions.append(instruction)
    
    def get_cut_instructions(self) -> List[CutInstruction]:
        """Vágási utasítások visszaadása"""
        return self.cut_instructions.copy()
    
    def clear_cut_instructions(self):
        """Vágási utasítások törlése"""
        self.cut_instructions = []
    
    def get_panel_coverage_info(self) -> Dict[str, Any]:
        """Panel lefedettségi információk"""
        if not self.active_panel or not self.placed_pieces:
            return {
                'panel_area': 0,
                'covered_area': 0,
                'coverage_percentage': 0,
                'pieces_count': 0
            }
        
        panel_area = self.active_panel.area
        covered_area = 0
        
        panel_bounds = self.active_panel.bounds
        
        for placed_piece in self.placed_pieces:
            intersection = self._calculate_intersection(placed_piece, panel_bounds)
            if intersection:
                covered_area += intersection['area']
        
        coverage_percentage = (covered_area / panel_area * 100) if panel_area > 0 else 0
        
        return {
            'panel_area': panel_area,
            'covered_area': covered_area,
            'coverage_percentage': coverage_percentage,
            'pieces_count': len(self.placed_pieces)
        }
    
    def validate_panel_completion(self) -> Dict[str, Any]:
        """Panel befejezettségének ellenőrzése"""
        coverage_info = self.get_panel_coverage_info()
        
        is_complete = coverage_info['coverage_percentage'] >= 95  # 95% lefedettség = kész
        is_overfilled = coverage_info['coverage_percentage'] > 105  # 105% = túltöltött
        
        return {
            'is_complete': is_complete,
            'is_overfilled': is_overfilled,
            'coverage': coverage_info['coverage_percentage'],
            'status': 'complete' if is_complete else 'overfilled' if is_overfilled else 'incomplete'
        }
