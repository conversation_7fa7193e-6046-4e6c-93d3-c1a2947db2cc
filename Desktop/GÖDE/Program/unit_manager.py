"""
Mértékegység kezelő modul
Kezeli a DXF fájlok mértékegységeinek konverzióját
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Optional, Tuple


class UnitManager:
    """Mértékegység kezel<PERSON> osztály"""
    
    def __init__(self):
        # Támogatott mértékegységek és mm-re való átváltási szorzók
        self.units = {
            "mm": {"name": "Milliméter", "factor": 1.0, "symbol": "mm"},
            "cm": {"name": "Centiméter", "factor": 10.0, "symbol": "cm"},
            "m": {"name": "Méter", "factor": 1000.0, "symbol": "m"},
            "inch": {"name": "Hüvelyk", "factor": 25.4, "symbol": "\""},
            "ft": {"name": "<PERSON>áb", "factor": 304.8, "symbol": "'"},
            "point": {"name": "Pont (1/72 inch)", "factor": 0.352778, "symbol": "pt"},
        }
        
        self.current_unit = "mm"  # Alapértelmezett egység
        self.scale_factor = 1.0   # Jelenlegi skálázási tényező
    
    def get_available_units(self) -> Dict[str, Dict]:
        """Elérhető mértékegységek visszaadása"""
        return self.units
    
    def set_unit(self, unit_key: str) -> bool:
        """Mértékegység beállítása"""
        if unit_key in self.units:
            self.current_unit = unit_key
            self.scale_factor = self.units[unit_key]["factor"]
            return True
        return False
    
    def get_current_unit(self) -> str:
        """Jelenlegi mértékegység visszaadása"""
        return self.current_unit
    
    def get_scale_factor(self) -> float:
        """Jelenlegi skálázási tényező visszaadása"""
        return self.scale_factor
    
    def convert_to_mm(self, value: float) -> float:
        """Érték konvertálása mm-re"""
        return value * self.scale_factor
    
    def convert_from_mm(self, value_mm: float) -> float:
        """Érték konvertálása mm-ről a jelenlegi egységre"""
        return value_mm / self.scale_factor
    
    def format_value(self, value: float, precision: int = 2) -> str:
        """Érték formázása a jelenlegi egységgel"""
        unit_info = self.units[self.current_unit]
        converted_value = self.convert_from_mm(value)
        return f"{converted_value:.{precision}f} {unit_info['symbol']}"
    
    def get_unit_info(self, unit_key: str = None) -> Dict:
        """Mértékegység információk visszaadása"""
        if unit_key is None:
            unit_key = self.current_unit
        return self.units.get(unit_key, {})


class UnitSelectionDialog:
    """Mértékegység kiválasztó dialógus"""
    
    def __init__(self, parent, unit_manager: UnitManager):
        self.parent = parent
        self.unit_manager = unit_manager
        self.result = None
        self.selected_unit = unit_manager.get_current_unit()
    
    def show_unit_selection_dialog(self, filename: str = "") -> Optional[str]:
        """Mértékegység kiválasztó dialógus megjelenítése"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("DXF Mértékegység beállítása")
        dialog.geometry("500x400")
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Középre igazítás
        dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 100,
            self.parent.winfo_rooty() + 100
        ))
        
        # Fő frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Cím
        if filename:
            title_text = f"DXF fájl: {filename}"
        else:
            title_text = "DXF fájl mértékegység beállítása"
        
        title_label = ttk.Label(main_frame, text=title_text, font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))
        
        # Leírás
        desc_text = ("Válassza ki, hogy a DXF fájlban lévő számértékek\n"
                    "milyen mértékegységben értendők:")
        desc_label = ttk.Label(main_frame, text=desc_text, font=("Arial", 10))
        desc_label.pack(pady=(0, 20))
        
        # Mértékegység választó
        unit_frame = ttk.LabelFrame(main_frame, text="Mértékegység", padding="10")
        unit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Változó a kiválasztott egységhez
        selected_var = tk.StringVar(value=self.selected_unit)
        
        # Rádiógombok
        units = self.unit_manager.get_available_units()
        for unit_key, unit_info in units.items():
            radio_text = f"{unit_info['name']} ({unit_info['symbol']})"
            
            # Példa érték hozzáadása
            example_value = 120.0  # Példa érték
            example_mm = example_value * unit_info['factor']
            radio_text += f" - Példa: 120 → {example_mm:.1f} mm"
            
            radio = ttk.Radiobutton(
                unit_frame,
                text=radio_text,
                variable=selected_var,
                value=unit_key
            )
            radio.pack(anchor=tk.W, pady=2)
        
        # Előnézet frame
        preview_frame = ttk.LabelFrame(main_frame, text="Előnézet", padding="10")
        preview_frame.pack(fill=tk.X, pady=(0, 20))
        
        preview_label = ttk.Label(preview_frame, text="", font=("Arial", 9))
        preview_label.pack()
        
        def update_preview():
            """Előnézet frissítése"""
            current_unit = selected_var.get()
            if current_unit in units:
                unit_info = units[current_unit]
                
                # Példa konverziók
                examples = [
                    ("Egy 120 egység hosszú vonal", 120.0),
                    ("Egy 50 egység széles panel", 50.0),
                    ("Egy 300 egység magas elem", 300.0)
                ]
                
                preview_text = f"Kiválasztott egység: {unit_info['name']}\n\n"
                
                for desc, value in examples:
                    mm_value = value * unit_info['factor']
                    if mm_value >= 1000:
                        display_value = f"{mm_value/1000:.2f} m"
                    elif mm_value >= 10:
                        display_value = f"{mm_value:.0f} mm"
                    else:
                        display_value = f"{mm_value:.1f} mm"
                    
                    preview_text += f"{desc}: {display_value}\n"
                
                preview_label.config(text=preview_text)
        
        # Előnézet frissítése változáskor
        def on_unit_change():
            update_preview()
        
        # Eseménykezelők hozzáadása
        for unit_key in units.keys():
            # Sajnos a ttk.Radiobutton nem támogatja a command paramétert
            # Ezért egy másik megoldást használunk
            pass
        
        # Kezdeti előnézet
        update_preview()
        
        # Gombok
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def on_ok():
            self.result = selected_var.get()
            update_preview()  # Utolsó frissítés
            dialog.destroy()
        
        def on_cancel():
            self.result = None
            dialog.destroy()
        
        # Gomb elrendezés
        ttk.Button(button_frame, text="Mégse", command=on_cancel).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.RIGHT)
        
        # Alapértelmezett kiválasztás
        if self.selected_unit in units:
            selected_var.set(self.selected_unit)
        
        # Frissítés gomb az előnézet teszteléséhez
        refresh_button = ttk.Button(button_frame, text="Előnézet frissítése", command=update_preview)
        refresh_button.pack(side=tk.RIGHT, padx=(0, 10))
        
        # Enter és Escape billentyűk
        dialog.bind('<Return>', lambda e: on_ok())
        dialog.bind('<Escape>', lambda e: on_cancel())
        
        # Fókusz beállítása
        dialog.focus_set()
        
        # Várakozás a dialógus bezárására
        dialog.wait_window()
        
        return self.result


def test_unit_manager():
    """Unit manager tesztelése"""
    um = UnitManager()
    
    print("Elérhető mértékegységek:")
    for key, info in um.get_available_units().items():
        print(f"  {key}: {info['name']} ({info['symbol']}) - szorzó: {info['factor']}")
    
    print(f"\nJelenlegi egység: {um.get_current_unit()}")
    print(f"Skálázási tényező: {um.get_scale_factor()}")
    
    # Tesztelés különböző egységekkel
    test_value = 120.0
    
    for unit in ["mm", "cm", "m", "inch"]:
        um.set_unit(unit)
        mm_value = um.convert_to_mm(test_value)
        formatted = um.format_value(mm_value)
        print(f"{test_value} {unit} = {mm_value:.2f} mm = {formatted}")


if __name__ == "__main__":
    test_unit_manager()
