"""
Teljes rendszer teszt - minden funkció együtt
"""

from dxf_parser import DXFParser
from panel_manager import PanelManager
from standard_sheet_manager import StandardSheetManager
from unit_manager import UnitManager


def test_complete_workflow():
    """Teljes munkafolyamat tesztelése"""
    
    print("🚀 DXF VIEWER - TELJES RENDSZER TESZT")
    print("=" * 50)
    
    # 1. Mértékegység kezelés
    print("\n1️⃣ MÉRTÉKEGYSÉG KEZELÉS")
    unit_manager = UnitManager()
    
    # Centiméter egység beállítása (tipikus gipszkarton méretek)
    unit_manager.set_unit("cm")
    scale_factor = unit_manager.get_scale_factor()
    
    print(f"   ✅ Egység beállítva: {unit_manager.get_current_unit()}")
    print(f"   📏 Skálázási tényező: {scale_factor}")
    
    # 2. DXF betöltés mértékegységgel
    print("\n2️⃣ DXF FÁJL BETÖLTÉSE")
    parser = DXFParser(unit_manager)
    success = parser.load_dxf("GODEFEHER.dxf", scale_factor)
    
    if success:
        entities = parser.get_entities()
        bounds = parser.get_bounds()
        
        # Méretek cm-ben
        width_mm = bounds[2] - bounds[0]
        height_mm = bounds[3] - bounds[1]
        width_cm = unit_manager.convert_from_mm(width_mm)
        height_cm = unit_manager.convert_from_mm(height_mm)
        
        print(f"   ✅ DXF betöltve: {len(entities)} entitás")
        print(f"   📐 Rajz mérete: {width_cm:.1f} x {height_cm:.1f} cm")
        print(f"   📐 Rajz mérete: {width_mm:.0f} x {height_mm:.0f} mm")
    else:
        print("   ❌ DXF betöltés sikertelen")
        return
    
    # 3. Panel felismerés
    print("\n3️⃣ PANEL FELISMERÉS")
    panel_manager = PanelManager()
    panels = panel_manager.find_closed_shapes(entities)
    
    print(f"   ✅ {len(panels)} panel felismerve")
    
    if panels:
        # Panel statisztikák
        areas_mm2 = [panel.area for panel in panels]
        total_area_mm2 = sum(areas_mm2)
        total_area_m2 = total_area_mm2 / 1000000  # mm² → m²
        
        largest_panel = max(panels, key=lambda p: p.area)
        smallest_panel = min(panels, key=lambda p: p.area)
        
        print(f"   📊 Összes panel terület: {total_area_m2:.2f} m²")
        print(f"   📊 Legnagyobb panel: {largest_panel.area/10000:.2f} cm²")
        print(f"   📊 Legkisebb panel: {smallest_panel.area/10000:.2f} cm²")
        
        # Panel részletek
        print(f"   📋 Panel részletek:")
        for i, panel in enumerate(panels[:5]):  # Első 5 panel
            area_cm2 = panel.area / 100  # mm² → cm²
            perimeter_cm = panel.perimeter / 10  # mm → cm
            print(f"      Panel {panel.id}: {area_cm2:.0f} cm², kerület: {perimeter_cm:.0f} cm")
    
    # 4. Szabványlapok
    print("\n4️⃣ SZABVÁNYLAPOK KEZELÉSE")
    sheet_manager = StandardSheetManager()
    
    # Alapértelmezett típusok
    sheet_types = sheet_manager.get_sheet_types()
    print(f"   ✅ {len(sheet_types)} alapértelmezett típus")
    
    # Tipikus gipszkarton méretek hozzáadása
    sheet_manager.add_sheets("GKB 1200x2000", 5)
    sheet_manager.add_sheets("GKB 1200x2500", 3)
    sheet_manager.add_sheets("GKB 600x2000", 2)
    
    sheets = sheet_manager.get_sheets()
    print(f"   ✅ {len(sheets)} szabványlap hozzáadva")
    
    # Lapok pozicionálása
    sheet_manager.set_sheet_area_position(bounds)
    print(f"   📍 Lapok pozicionálva a rajz mellett")
    
    # Szabványlap statisztikák
    total_sheet_area_mm2 = sum(sheet.width * sheet.height for sheet in sheets)
    total_sheet_area_m2 = total_sheet_area_mm2 / 1000000
    
    print(f"   📊 Összes lap terület: {total_sheet_area_m2:.2f} m²")
    
    # 5. Optimalizálási potenciál
    print("\n5️⃣ OPTIMALIZÁLÁSI ELEMZÉS")
    
    if panels and sheets:
        panel_area_m2 = sum(panel.area for panel in panels) / 1000000
        sheet_area_m2 = total_sheet_area_m2
        
        coverage_ratio = panel_area_m2 / sheet_area_m2 * 100
        waste_ratio = 100 - coverage_ratio
        
        print(f"   📊 Panel terület: {panel_area_m2:.2f} m²")
        print(f"   📊 Lap terület: {sheet_area_m2:.2f} m²")
        print(f"   📊 Kihasználtság: {coverage_ratio:.1f}%")
        print(f"   📊 Hulladék: {waste_ratio:.1f}%")
        
        if coverage_ratio > 100:
            print(f"   ⚠️  Több lap szükséges!")
        elif coverage_ratio > 80:
            print(f"   ✅ Jó kihasználtság")
        else:
            print(f"   💡 Optimalizálási lehetőség")
    
    # 6. Koordináta példák
    print("\n6️⃣ KOORDINÁTA MEGJELENÍTÉS")
    
    # Példa koordináták különböző egységekben
    test_points = [
        (1000, 1500),  # mm
        (500, 750),    # mm
        (2000, 2500)   # mm
    ]
    
    for unit in ["mm", "cm", "m"]:
        unit_manager.set_unit(unit)
        print(f"   {unit.upper()} egységben:")
        
        for x_mm, y_mm in test_points:
            x_formatted = unit_manager.format_value(x_mm, 1)
            y_formatted = unit_manager.format_value(y_mm, 1)
            print(f"      ({x_mm}, {y_mm}) mm → X: {x_formatted}, Y: {y_formatted}")
    
    # 7. Összefoglalás
    print("\n🎯 ÖSSZEFOGLALÁS")
    print("=" * 30)
    print(f"✅ DXF fájl: {len(entities)} entitás")
    print(f"✅ Mértékegység: {unit_manager.get_current_unit()} (skála: {scale_factor}x)")
    print(f"✅ Panelek: {len(panels)} zárt alakzat")
    print(f"✅ Szabványlapok: {len(sheets)} lap")
    print(f"✅ Koordináta kezelés: működik")
    print(f"✅ Drag & Drop: implementálva")
    
    print(f"\n🚀 A rendszer készen áll a gipszkarton optimalizálásra!")
    print(f"   📋 Következő lépés: Panel kiválasztás és optimalizálási algoritmus")


if __name__ == "__main__":
    test_complete_workflow()
