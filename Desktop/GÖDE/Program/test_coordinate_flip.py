"""
Koordin<PERSON><PERSON> tükrözés tesztelése
"""

from dxf_parser import DXFParser


def test_coordinate_flip():
    """Koordináta tükrözés tesztelése"""
    
    parser = DXFParser()
    success = parser.load_dxf("GODEFEHER.dxf")
    
    if not success:
        print("Hiba: Nem sikerült betölteni a DXF fájlt!")
        return
    
    entities = parser.get_entities()
    bounds = parser.get_bounds()
    
    print(f"Eredeti DXF koordináták (tükrözés után):")
    print(f"Rajz határai: {bounds}")
    print(f"Szélesség: {bounds[2] - bounds[0]:.2f}")
    print(f"Magasság: {bounds[3] - bounds[1]:.2f}")
    
    print("\nElső 5 vonal koordinátái (tükrözés után):")
    line_count = 0
    for entity in entities:
        if entity['type'] == 'line' and line_count < 5:
            print(f"Vonal {line_count + 1}: {entity['start']} -> {entity['end']}")
            line_count += 1
    
    # Y koordináták ellenőrzése
    all_y = []
    for entity in entities:
        if entity['type'] == 'line':
            all_y.extend([entity['start'][1], entity['end'][1]])
    
    print(f"\nY koordináták tartománya (tükrözés után): {min(all_y):.2f} - {max(all_y):.2f}")
    
    # Ellenőrizzük, hogy a tükrözés megtörtént-e
    if max(all_y) <= 0:
        print("✅ Tükrözés sikeres - minden Y koordináta negatív vagy nulla")
    else:
        print("❌ Tükrözés nem teljes - vannak pozitív Y koordináták")


if __name__ == "__main__":
    test_coordinate_flip()
