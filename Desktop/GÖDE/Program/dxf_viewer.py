"""
DXF Viewer - AutoCAD-szerű DXF fájl megjelenítő
Tkinter alapú alkalmazás végtelen canvas-szal, zoom és pan funkciókkal
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys

# Saját modulok importálása
from dxf_parser import DXFParser
from canvas_manager import CanvasManager
from geometry_renderer import GeometryRenderer
from panel_manager import PanelManager


class DXFViewer:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DXF Viewer")
        self.root.geometry("1200x800")
        self.root.state('zoomed')  # Maximalizált ablak Windows-on
        
        # Komponensek
        self.dxf_parser = DXFParser()
        self.canvas_manager = None
        self.renderer = None
        self.panel_manager = PanelManager()
        
        # UI elemek
        self.canvas = None
        self.status_bar = None
        self.layer_frame = None
        self.layer_listbox = None
        
        # Aktuális fájl
        self.current_file = None
        
        self._create_ui()
        self._setup_menu()
        
        # Ablak bezárás esemény
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_ui(self):
        """Felhasználói felület létrehozása"""
        # Fő frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Eszköztár
        self._create_toolbar(main_frame)
        
        # Fő tartalom terület
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Bal oldali panel (rétegek)
        self._create_layer_panel(content_frame)
        
        # Canvas terület
        self._create_canvas_area(content_frame)
        
        # Állapotsor
        self._create_status_bar(main_frame)
    
    def _create_toolbar(self, parent):
        """Eszköztár létrehozása"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # Fájl műveletek
        ttk.Button(toolbar, text="Megnyitás", command=self.open_file).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # Zoom műveletek
        ttk.Button(toolbar, text="Zoom In", command=self.zoom_in).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Zoom Out", command=self.zoom_out).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar, text="Teljes nézet", command=self.fit_to_window).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        # Panel műveletek
        self.panel_select_button = ttk.Button(toolbar, text="Panel kiválasztása", command=self.toggle_panel_selection)
        self.panel_select_button.pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)
        
        # Információk
        self.coord_label = ttk.Label(toolbar, text="X: 0.00, Y: 0.00")
        self.coord_label.pack(side=tk.RIGHT, padx=10)
        
        self.zoom_label = ttk.Label(toolbar, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.RIGHT, padx=10)
    
    def _create_layer_panel(self, parent):
        """Réteg panel létrehozása"""
        # Réteg frame
        layer_frame = ttk.LabelFrame(parent, text="Rétegek", width=200)
        layer_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        layer_frame.pack_propagate(False)
        
        # Réteg lista
        list_frame = ttk.Frame(layer_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Listbox
        self.layer_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set)
        self.layer_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.layer_listbox.yview)
        
        # Réteg műveletek
        button_frame = ttk.Frame(layer_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="Összes be", command=self.show_all_layers).pack(fill=tk.X, pady=1)
        ttk.Button(button_frame, text="Összes ki", command=self.hide_all_layers).pack(fill=tk.X, pady=1)
        
        # Réteg kattintás esemény
        self.layer_listbox.bind("<Double-Button-1>", self.toggle_layer)
    
    def _create_canvas_area(self, parent):
        """Canvas terület létrehozása"""
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Canvas scrollbar-okkal
        canvas_container = ttk.Frame(canvas_frame)
        canvas_container.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar-ok
        v_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        h_scrollbar = ttk.Scrollbar(canvas_container, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Canvas
        self.canvas = tk.Canvas(
            canvas_container,
            bg="#000000",  # Fekete háttér (AutoCAD-szerű)
            scrollregion=(-10000, -10000, 10000, 10000),
            yscrollcommand=v_scrollbar.set,
            xscrollcommand=h_scrollbar.set
        )
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar-ok konfigurálása
        v_scrollbar.config(command=self.canvas.yview)
        h_scrollbar.config(command=self.canvas.xview)
        
        # Canvas manager és renderer inicializálása
        self.canvas_manager = CanvasManager(self.canvas)
        self.renderer = GeometryRenderer(self.canvas, self.canvas_manager)
        self.renderer.set_panel_manager(self.panel_manager)

        # Egér mozgás követése koordináták megjelenítéséhez
        self.canvas.bind("<Motion>", self._on_mouse_motion)

        # Canvas kattintás panel kiválasztáshoz
        self.canvas.bind("<Button-1>", self._on_canvas_click)

        # Canvas méret változás esemény
        self.canvas.bind("<Configure>", self._on_canvas_configure)
    
    def _create_status_bar(self, parent):
        """Állapotsor létrehozása"""
        self.status_bar = ttk.Label(parent, text="Kész", relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=2)

    def _setup_menu(self):
        """Menürendszer beállítása"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Fájl menü
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Fájl", menu=file_menu)
        file_menu.add_command(label="Megnyitás...", command=self.open_file, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="Kilépés", command=self._on_closing, accelerator="Ctrl+Q")

        # Nézet menü
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Nézet", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="+")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="-")
        view_menu.add_command(label="Teljes nézet", command=self.fit_to_window, accelerator="Home")
        view_menu.add_separator()
        view_menu.add_command(label="Összes réteg be", command=self.show_all_layers)
        view_menu.add_command(label="Összes réteg ki", command=self.hide_all_layers)

        # Súgó menü
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Súgó", menu=help_menu)
        help_menu.add_command(label="Kezelés", command=self.show_help)
        help_menu.add_command(label="Névjegy", command=self.show_about)

        # Billentyű kombinációk
        self.root.bind("<Control-o>", lambda e: self.open_file())
        self.root.bind("<Control-q>", lambda e: self._on_closing())

    # Eseménykezelők
    def _on_mouse_motion(self, event):
        """Egér mozgás eseménykezelő - koordináták megjelenítése"""
        if self.canvas_manager:
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)
            world_x, world_y = self.canvas_manager.canvas_to_world(canvas_x, canvas_y)

            self.coord_label.config(text=f"X: {world_x:.2f}, Y: {world_y:.2f}")

            # Zoom szint megjelenítése
            zoom_percent = self.canvas_manager.scale * 100
            self.zoom_label.config(text=f"Zoom: {zoom_percent:.1f}%")

    def _on_canvas_click(self, event):
        """Canvas kattintás eseménykezelő - panel kiválasztás"""
        if not self.panel_manager.is_selection_mode():
            return

        # Canvas koordináták világkoordinátákra
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        world_x, world_y = self.canvas_manager.canvas_to_world(canvas_x, canvas_y)

        # Panel kiválasztása
        selected_panel = self.panel_manager.select_panel_at_point(world_x, world_y)

        if selected_panel:
            self.status_bar.config(text=f"Panel {selected_panel.id} kiválasztva (Terület: {selected_panel.area:.2f})")
        else:
            self.status_bar.config(text="Nincs panel a kattintott helyen")

        # Újrarajzolás a kiemelés megjelenítéséhez
        self.renderer.render()

    def _on_canvas_configure(self, event):
        """Canvas méret változás eseménykezelő"""
        # Canvas scroll régió frissítése
        if self.canvas_manager:
            visible_bounds = self.canvas_manager.get_visible_bounds()
            margin = 1000
            scroll_region = (
                visible_bounds[0] - margin,
                visible_bounds[1] - margin,
                visible_bounds[2] + margin,
                visible_bounds[3] + margin
            )
            self.canvas.configure(scrollregion=scroll_region)

    def _on_closing(self):
        """Alkalmazás bezárás eseménykezelő"""
        self.root.quit()
        self.root.destroy()

    # Fájl műveletek
    def open_file(self):
        """DXF fájl megnyitása"""
        file_path = filedialog.askopenfilename(
            title="DXF fájl megnyitása",
            filetypes=[
                ("DXF fájlok", "*.dxf"),
                ("Minden fájl", "*.*")
            ]
        )

        if file_path:
            self.load_dxf_file(file_path)

    def load_dxf_file(self, file_path: str):
        """DXF fájl betöltése és megjelenítése"""
        try:
            self.status_bar.config(text="DXF fájl betöltése...")
            self.root.update()

            # DXF fájl betöltése
            if self.dxf_parser.load_dxf(file_path):
                self.current_file = file_path

                # Adatok átadása a renderelőnek
                entities = self.dxf_parser.get_entities()
                layers = self.dxf_parser.get_layers()
                self.renderer.set_data(entities, layers)

                # Panelek keresése
                panels = self.panel_manager.find_closed_shapes(entities)

                # Rétegek lista frissítése
                self._update_layer_list()

                # Teljes nézet beállítása
                bounds = self.dxf_parser.get_bounds()
                self.canvas_manager.fit_to_window(bounds)

                # Ablak címének frissítése
                filename = os.path.basename(file_path)
                self.root.title(f"DXF Viewer - {filename}")

                panel_count = len(self.panel_manager.get_panels())
                self.status_bar.config(text=f"Betöltve: {filename} ({len(entities)} elem, {panel_count} panel)")

            else:
                messagebox.showerror("Hiba", "Nem sikerült betölteni a DXF fájlt!")
                self.status_bar.config(text="Hiba a betöltés során")

        except Exception as e:
            messagebox.showerror("Hiba", f"Hiba történt a fájl betöltése során:\n{str(e)}")
            self.status_bar.config(text="Hiba a betöltés során")

    def _update_layer_list(self):
        """Rétegek listájának frissítése"""
        self.layer_listbox.delete(0, tk.END)

        layers = self.dxf_parser.get_layers()
        for layer_name, layer_info in layers.items():
            visible_mark = "●" if layer_info['visible'] else "○"
            entity_count = len(layer_info['entities'])
            display_text = f"{visible_mark} {layer_name} ({entity_count})"
            self.layer_listbox.insert(tk.END, display_text)

    # Zoom és nézet műveletek
    def zoom_in(self):
        """Zoom in művelet"""
        if self.canvas_manager:
            self.canvas_manager.zoom_in()

    def zoom_out(self):
        """Zoom out művelet"""
        if self.canvas_manager:
            self.canvas_manager.zoom_out()

    def fit_to_window(self):
        """Teljes nézet - rajz illesztése az ablakhoz"""
        if self.canvas_manager and self.dxf_parser.entities:
            bounds = self.dxf_parser.get_bounds()
            self.canvas_manager.fit_to_window(bounds)

    # Panel műveletek
    def toggle_panel_selection(self):
        """Panel kiválasztási mód váltása"""
        current_mode = self.panel_manager.is_selection_mode()
        new_mode = not current_mode

        self.panel_manager.set_selection_mode(new_mode)

        if new_mode:
            self.panel_select_button.config(text="Kiválasztás vége", relief="sunken")
            self.status_bar.config(text="Panel kiválasztási mód - kattintson egy panelre")
            self.canvas.config(cursor="crosshair")
        else:
            self.panel_select_button.config(text="Panel kiválasztása", relief="raised")
            self.status_bar.config(text="Panel kiválasztási mód kikapcsolva")
            self.canvas.config(cursor="")

        # Újrarajzolás a panelek megjelenítéséhez/elrejtéséhez
        self.renderer.render()

    # Réteg műveletek
    def toggle_layer(self, event):
        """Réteg láthatóságának váltása dupla kattintásra"""
        selection = self.layer_listbox.curselection()
        if selection:
            index = selection[0]
            layer_names = list(self.dxf_parser.get_layers().keys())
            if index < len(layer_names):
                layer_name = layer_names[index]
                self.renderer.toggle_layer_visibility(layer_name)
                self._update_layer_list()

    def show_all_layers(self):
        """Összes réteg megjelenítése"""
        layers = self.dxf_parser.get_layers()
        for layer_info in layers.values():
            layer_info['visible'] = True
        self.renderer.render()
        self._update_layer_list()

    def hide_all_layers(self):
        """Összes réteg elrejtése"""
        layers = self.dxf_parser.get_layers()
        for layer_info in layers.values():
            layer_info['visible'] = False
        self.renderer.render()
        self._update_layer_list()

    # Súgó műveletek
    def show_help(self):
        """Kezelési útmutató megjelenítése"""
        help_text = """
DXF Viewer - Kezelési útmutató

Navigáció:
• Egérkerék: Zoom in/out
• Bal egérgomb húzás: Pan (mozgatás)
• + / - billentyűk: Zoom in/out
• Home billentyű: Teljes nézet

Rétegek:
• Dupla kattintás: Réteg be/ki kapcsolása
• Jobb oldali gombok: Összes réteg be/ki

Panel kiválasztás:
• "Panel kiválasztása" gomb: Kiválasztási mód be/ki
• Kiválasztási módban: Kattintás panelre kiválasztja
• Zöld keret: Elérhető panelek
• Piros keret: Aktív panel

Fájl műveletek:
• Ctrl+O: Fájl megnyitása
• Ctrl+Q: Kilépés

Támogatott DXF elemek:
• Vonalak (LINE)
• Körök (CIRCLE)
• Ívek (ARC)
• Polyline-ok
• Szövegek (TEXT)

Automatikus panel felismerés:
• A program automatikusan felismeri a zárt alakzatokat
• Minden zárt alakzat egy panelként kezelhető
        """
        messagebox.showinfo("Kezelési útmutató", help_text)

    def show_about(self):
        """Névjegy megjelenítése"""
        about_text = """
DXF Viewer v1.0

AutoCAD-szerű DXF fájl megjelenítő
Tkinter alapú Python alkalmazás

Fejlesztő: AI Assistant
Licenc: MIT

Használt könyvtárak:
• tkinter (GUI)
• ezdxf (DXF parsing)
        """
        messagebox.showinfo("Névjegy", about_text)

    def run(self):
        """Alkalmazás indítása"""
        self.root.mainloop()


def main():
    """Fő függvény"""
    try:
        app = DXFViewer()
        app.run()
    except Exception as e:
        print(f"Hiba az alkalmazás indításakor: {e}")
        messagebox.showerror("Kritikus hiba", f"Az alkalmazás nem indítható:\n{str(e)}")


if __name__ == "__main__":
    main()
