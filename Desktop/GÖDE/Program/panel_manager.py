"""
Panel kezelő modul
Kezeli a zárt alakzatok (panelek) azonosítását és kiválasztását
"""

import math
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass


@dataclass
class Panel:
    """Panel (zárt alakzat) adatstruktúra"""
    id: int
    entities: List[Dict[str, Any]]  # A panelt alkotó entitások
    bounds: Tuple[float, float, float, float]  # min_x, min_y, max_x, max_y
    area: float
    perimeter: float
    center: Tuple[float, float]
    is_active: bool = False
    color: str = "#FFFF00"  # Sárga alapértelmezett


class PanelManager:
    def __init__(self):
        self.panels: List[Panel] = []
        self.active_panel: Optional[Panel] = None
        self.selection_mode: bool = False
        
    def find_closed_shapes(self, entities: List[Dict[str, Any]]) -> List[Panel]:
        """Zárt alakzatok keresése az entitások között"""
        self.panels = []
        
        # Vonalak gyűjtése
        lines = [e for e in entities if e['type'] == 'line']
        
        if not lines:
            return self.panels
        
        # Zárt alakzatok keresése
        closed_shapes = self._find_closed_loops(lines)
        
        # Panel objektumok létrehozása
        for i, shape_lines in enumerate(closed_shapes):
            panel = self._create_panel_from_lines(i, shape_lines)
            if panel:
                self.panels.append(panel)
        
        return self.panels
    
    def _find_closed_loops(self, lines: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Zárt hurkok keresése vonalak között"""
        closed_shapes = []
        used_lines = set()
        
        for i, start_line in enumerate(lines):
            if i in used_lines:
                continue
                
            # Próbáljunk zárt hurkot találni ezzel a vonallal kezdve
            loop = self._trace_loop(start_line, lines, used_lines)
            
            if loop and len(loop) >= 3:  # Minimum 3 vonal kell egy zárt alakzathoz
                closed_shapes.append(loop)
                # Jelöljük meg a használt vonalakat
                for line in loop:
                    for j, original_line in enumerate(lines):
                        if self._lines_equal(line, original_line):
                            used_lines.add(j)
                            break
        
        return closed_shapes
    
    def _trace_loop(self, start_line: Dict[str, Any], all_lines: List[Dict[str, Any]], 
                   used_lines: set, tolerance: float = 1e-6) -> Optional[List[Dict[str, Any]]]:
        """Zárt hurok követése egy kezdő vonaltól"""
        loop = [start_line]
        current_end = start_line['end']
        start_point = start_line['start']
        
        max_iterations = len(all_lines)  # Végtelen ciklus elkerülése
        
        for _ in range(max_iterations):
            # Keressünk olyan vonalat, ami csatlakozik a jelenlegi végponthoz
            next_line = None
            
            for i, line in enumerate(all_lines):
                if i in used_lines:
                    continue
                    
                # Ellenőrizzük, hogy ez a vonal már benne van-e a hurokban
                if any(self._lines_equal(line, loop_line) for loop_line in loop):
                    continue
                
                # Ellenőrizzük a csatlakozást
                if self._points_close(current_end, line['start'], tolerance):
                    next_line = line
                    current_end = line['end']
                    break
                elif self._points_close(current_end, line['end'], tolerance):
                    # Fordított irányú vonal
                    next_line = {
                        'type': line['type'],
                        'start': line['end'],
                        'end': line['start'],
                        'layer': line['layer'],
                        'color': line['color']
                    }
                    current_end = next_line['end']
                    break
            
            if next_line is None:
                # Nem találtunk folytatást
                break
                
            loop.append(next_line)
            
            # Ellenőrizzük, hogy visszaértünk-e a kezdőponthoz
            if self._points_close(current_end, start_point, tolerance):
                return loop
        
        return None
    
    def _points_close(self, p1: Tuple[float, float], p2: Tuple[float, float], 
                     tolerance: float = 1e-6) -> bool:
        """Két pont közelsége ellenőrzése"""
        dx = p1[0] - p2[0]
        dy = p1[1] - p2[1]
        return (dx * dx + dy * dy) < (tolerance * tolerance)
    
    def _lines_equal(self, line1: Dict[str, Any], line2: Dict[str, Any]) -> bool:
        """Két vonal egyenlőségének ellenőrzése"""
        return (self._points_close(line1['start'], line2['start']) and 
                self._points_close(line1['end'], line2['end'])) or \
               (self._points_close(line1['start'], line2['end']) and 
                self._points_close(line1['end'], line2['start']))
    
    def _create_panel_from_lines(self, panel_id: int, lines: List[Dict[str, Any]]) -> Optional[Panel]:
        """Panel objektum létrehozása vonalakból"""
        if not lines:
            return None
        
        # Határok számítása
        all_points = []
        for line in lines:
            all_points.extend([line['start'], line['end']])
        
        if not all_points:
            return None
        
        xs = [p[0] for p in all_points]
        ys = [p[1] for p in all_points]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        bounds = (min_x, min_y, max_x, max_y)
        center = ((min_x + max_x) / 2, (min_y + max_y) / 2)
        
        # Terület és kerület számítása (egyszerűsített)
        perimeter = sum(self._line_length(line) for line in lines)
        area = self._calculate_polygon_area(lines)
        
        return Panel(
            id=panel_id,
            entities=lines,
            bounds=bounds,
            area=area,
            perimeter=perimeter,
            center=center
        )
    
    def _line_length(self, line: Dict[str, Any]) -> float:
        """Vonal hosszának számítása"""
        dx = line['end'][0] - line['start'][0]
        dy = line['end'][1] - line['start'][1]
        return math.sqrt(dx * dx + dy * dy)
    
    def _calculate_polygon_area(self, lines: List[Dict[str, Any]]) -> float:
        """Polygon területének számítása (Shoelace formula)"""
        # Pontok sorrendbe rendezése
        points = self._order_points(lines)
        
        if len(points) < 3:
            return 0.0
        
        area = 0.0
        n = len(points)
        
        for i in range(n):
            j = (i + 1) % n
            area += points[i][0] * points[j][1]
            area -= points[j][0] * points[i][1]
        
        return abs(area) / 2.0
    
    def _order_points(self, lines: List[Dict[str, Any]]) -> List[Tuple[float, float]]:
        """Pontok sorrendbe rendezése a zárt alakzat mentén"""
        if not lines:
            return []
        
        points = [lines[0]['start']]
        current_end = lines[0]['end']
        used_lines = {0}
        
        while len(used_lines) < len(lines):
            found = False
            for i, line in enumerate(lines):
                if i in used_lines:
                    continue
                
                if self._points_close(current_end, line['start']):
                    points.append(line['start'])
                    current_end = line['end']
                    used_lines.add(i)
                    found = True
                    break
                elif self._points_close(current_end, line['end']):
                    points.append(line['end'])
                    current_end = line['start']
                    used_lines.add(i)
                    found = True
                    break
            
            if not found:
                break
        
        return points
    
    def select_panel_at_point(self, world_x: float, world_y: float) -> Optional[Panel]:
        """Panel kiválasztása egy pont alapján"""
        for panel in self.panels:
            if self._point_in_panel(world_x, world_y, panel):
                # Előző aktív panel deaktiválása
                if self.active_panel:
                    self.active_panel.is_active = False
                
                # Új panel aktiválása
                panel.is_active = True
                self.active_panel = panel
                return panel
        
        # Ha nem találtunk panelt, deaktiváljuk az aktívat
        if self.active_panel:
            self.active_panel.is_active = False
            self.active_panel = None
        
        return None
    
    def _point_in_panel(self, x: float, y: float, panel: Panel) -> bool:
        """Ellenőrzi, hogy egy pont benne van-e a panelben"""
        # Egyszerű bounding box ellenőrzés először
        min_x, min_y, max_x, max_y = panel.bounds
        if not (min_x <= x <= max_x and min_y <= y <= max_y):
            return False
        
        # Ray casting algoritmus a pontos ellenőrzéshez
        points = self._order_points(panel.entities)
        if len(points) < 3:
            return False
        
        inside = False
        j = len(points) - 1
        
        for i in range(len(points)):
            xi, yi = points[i]
            xj, yj = points[j]
            
            if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
                inside = not inside
            j = i
        
        return inside
    
    def get_panels(self) -> List[Panel]:
        """Összes panel visszaadása"""
        return self.panels
    
    def get_active_panel(self) -> Optional[Panel]:
        """Aktív panel visszaadása"""
        return self.active_panel
    
    def set_selection_mode(self, enabled: bool):
        """Panel kiválasztási mód be/kikapcsolása"""
        self.selection_mode = enabled
    
    def is_selection_mode(self) -> bool:
        """Panel kiválasztási mód állapota"""
        return self.selection_mode
