"""
Szabványlapok kezelő modul
Kezeli a szabványos g<PERSON>ton lapok hozzáadását, törlését és megjelenítését
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import uuid


@dataclass
class StandardSheet:
    """Szabványlap adatstruktúra"""
    id: str
    name: str
    width: float
    height: float
    count: int
    color: str
    position: Tuple[float, float]  # Canvas pozíció
    is_dragging: bool = False
    original_count: int = 0  # Eredeti da<PERSON>m (visszaállításhoz)


class StandardSheetManager:
    def __init__(self):
        self.sheets: List[StandardSheet] = []
        self.sheet_types: Dict[str, Dict] = {}  # Típus definíciók
        self.next_position = [0, 0]  # Következő lap pozíciója
        self.grid_spacing = 150  # Rács távolság
        self.sheets_per_row = 5  # Lapok száma egy sorban
        
        # Alapértelmezett szabványlapok
        self._add_default_sheet_types()
    
    def _add_default_sheet_types(self):
        """Alapértelmezett szabványlap típusok hozzáadása"""
        self.sheet_types = {
            "GKB 1200x2000": {"width": 1200, "height": 2000, "color": "#FFE4B5"},
            "GKB 1200x2500": {"width": 1200, "height": 2500, "color": "#F0E68C"},
            "GKB 1200x3000": {"width": 1200, "height": 3000, "color": "#DDA0DD"},
            "GKB 600x2000": {"width": 600, "height": 2000, "color": "#98FB98"},
            "GKB 600x2500": {"width": 600, "height": 2500, "color": "#87CEEB"},
        }
    
    def add_sheet_type(self, name: str, width: float, height: float, color: str = None) -> bool:
        """Új szabványlap típus hozzáadása"""
        if not color:
            colors = ["#FFE4B5", "#F0E68C", "#DDA0DD", "#98FB98", "#87CEEB", 
                     "#FFA07A", "#20B2AA", "#9370DB", "#32CD32", "#FF6347"]
            color = colors[len(self.sheet_types) % len(colors)]
        
        self.sheet_types[name] = {
            "width": width,
            "height": height,
            "color": color
        }
        return True
    
    def remove_sheet_type(self, name: str) -> bool:
        """Szabványlap típus törlése"""
        if name in self.sheet_types:
            # Töröljük az összes ilyen típusú lapot
            self.sheets = [sheet for sheet in self.sheets if sheet.name != name]
            del self.sheet_types[name]
            self._reorganize_sheets()
            return True
        return False
    
    def add_sheets(self, sheet_type: str, count: int) -> bool:
        """Szabványlapok hozzáadása"""
        if sheet_type not in self.sheet_types:
            return False
        
        sheet_info = self.sheet_types[sheet_type]
        
        for i in range(count):
            position = self._get_next_position()
            sheet = StandardSheet(
                id=str(uuid.uuid4()),
                name=sheet_type,
                width=sheet_info["width"],
                height=sheet_info["height"],
                count=1,
                color=sheet_info["color"],
                position=position,
                original_count=1
            )
            self.sheets.append(sheet)
        
        return True
    
    def remove_sheet(self, sheet_id: str) -> bool:
        """Szabványlap törlése"""
        self.sheets = [sheet for sheet in self.sheets if sheet.id != sheet_id]
        self._reorganize_sheets()
        return True
    
    def _get_next_position(self) -> Tuple[float, float]:
        """Következő lap pozíciójának kiszámítása"""
        row = len(self.sheets) // self.sheets_per_row
        col = len(self.sheets) % self.sheets_per_row
        
        x = self.next_position[0] + col * self.grid_spacing
        y = self.next_position[1] + row * self.grid_spacing
        
        return (x, y)
    
    def _reorganize_sheets(self):
        """Lapok újrarendezése rács szerint"""
        for i, sheet in enumerate(self.sheets):
            row = i // self.sheets_per_row
            col = i % self.sheets_per_row
            
            x = self.next_position[0] + col * self.grid_spacing
            y = self.next_position[1] + row * self.grid_spacing
            
            sheet.position = (x, y)
    
    def set_sheet_area_position(self, bounds: Tuple[float, float, float, float]):
        """Szabványlapok területének pozíciójának beállítása"""
        # A DXF rajz mellett helyezzük el a lapokat
        min_x, min_y, max_x, max_y = bounds
        
        # Jobb oldalra helyezzük a lapokat
        self.next_position[0] = max_x + 200
        self.next_position[1] = min_y
        
        self._reorganize_sheets()
    
    def get_sheets(self) -> List[StandardSheet]:
        """Összes szabványlap visszaadása"""
        return self.sheets
    
    def get_sheet_types(self) -> Dict[str, Dict]:
        """Szabványlap típusok visszaadása"""
        return self.sheet_types
    
    def get_sheet_at_position(self, world_x: float, world_y: float) -> Optional[StandardSheet]:
        """Szabványlap keresése pozíció alapján"""
        for sheet in self.sheets:
            sheet_x, sheet_y = sheet.position
            
            # Ellenőrizzük, hogy a pont a lap területén belül van-e
            if (sheet_x <= world_x <= sheet_x + sheet.width and
                sheet_y <= world_y <= sheet_y + sheet.height):
                return sheet
        
        return None
    
    def move_sheet(self, sheet_id: str, new_position: Tuple[float, float]) -> bool:
        """Szabványlap mozgatása"""
        for sheet in self.sheets:
            if sheet.id == sheet_id:
                sheet.position = new_position
                return True
        return False
    
    def start_drag(self, sheet_id: str) -> bool:
        """Húzás kezdése"""
        for sheet in self.sheets:
            if sheet.id == sheet_id:
                sheet.is_dragging = True
                return True
        return False
    
    def stop_drag(self, sheet_id: str) -> bool:
        """Húzás befejezése"""
        for sheet in self.sheets:
            if sheet.id == sheet_id:
                sheet.is_dragging = False
                return True
        return False
    
    def get_dragging_sheet(self) -> Optional[StandardSheet]:
        """Húzott lap visszaadása"""
        for sheet in self.sheets:
            if sheet.is_dragging:
                return sheet
        return None


class StandardSheetDialog:
    """Szabványlap hozzáadása/szerkesztése dialógus"""
    
    def __init__(self, parent, sheet_manager: StandardSheetManager):
        self.parent = parent
        self.sheet_manager = sheet_manager
        self.result = None
    
    def show_add_sheet_type_dialog(self) -> Optional[Dict]:
        """Új szabványlap típus hozzáadása dialógus"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("Új szabványlap típus")
        dialog.geometry("400x300")
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Középre igazítás
        dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # Változók
        name_var = tk.StringVar()
        width_var = tk.StringVar()
        height_var = tk.StringVar()
        
        # UI elemek
        ttk.Label(dialog, text="Szabványlap típus neve:").pack(pady=5)
        name_entry = ttk.Entry(dialog, textvariable=name_var, width=30)
        name_entry.pack(pady=5)
        
        ttk.Label(dialog, text="Szélesség (mm):").pack(pady=5)
        width_entry = ttk.Entry(dialog, textvariable=width_var, width=30)
        width_entry.pack(pady=5)
        
        ttk.Label(dialog, text="Magasság (mm):").pack(pady=5)
        height_entry = ttk.Entry(dialog, textvariable=height_var, width=30)
        height_entry.pack(pady=5)
        
        # Gombok
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def on_ok():
            try:
                name = name_var.get().strip()
                width = float(width_var.get())
                height = float(height_var.get())
                
                if not name:
                    messagebox.showerror("Hiba", "A név nem lehet üres!")
                    return
                
                if width <= 0 or height <= 0:
                    messagebox.showerror("Hiba", "A méretek pozitívak kell legyenek!")
                    return
                
                self.result = {"name": name, "width": width, "height": height}
                dialog.destroy()
                
            except ValueError:
                messagebox.showerror("Hiba", "Érvénytelen számformátum!")
        
        def on_cancel():
            self.result = None
            dialog.destroy()
        
        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Mégse", command=on_cancel).pack(side=tk.LEFT, padx=5)
        
        # Alapértelmezett értékek
        name_entry.focus()
        
        # Várakozás a dialógus bezárására
        dialog.wait_window()
        
        return self.result
    
    def show_add_sheets_dialog(self) -> Optional[Dict]:
        """Szabványlapok hozzáadása dialógus"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("Szabványlapok hozzáadása")
        dialog.geometry("400x350")
        dialog.transient(self.parent)
        dialog.grab_set()
        
        # Középre igazítás
        dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        # Változók
        selected_type = tk.StringVar()
        count_var = tk.StringVar(value="1")
        
        # UI elemek
        ttk.Label(dialog, text="Válasszon szabványlap típust:").pack(pady=5)
        
        # Típus lista
        type_frame = ttk.Frame(dialog)
        type_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Listbox scrollbar-ral
        scrollbar = ttk.Scrollbar(type_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        type_listbox = tk.Listbox(type_frame, yscrollcommand=scrollbar.set)
        type_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=type_listbox.yview)
        
        # Típusok betöltése
        sheet_types = self.sheet_manager.get_sheet_types()
        for type_name, type_info in sheet_types.items():
            display_text = f"{type_name} ({type_info['width']}x{type_info['height']})"
            type_listbox.insert(tk.END, display_text)
        
        ttk.Label(dialog, text="Darabszám:").pack(pady=(10, 5))
        count_entry = ttk.Entry(dialog, textvariable=count_var, width=10)
        count_entry.pack(pady=5)
        
        # Gombok
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        
        def on_ok():
            try:
                selection = type_listbox.curselection()
                if not selection:
                    messagebox.showerror("Hiba", "Válasszon egy típust!")
                    return
                
                type_names = list(sheet_types.keys())
                selected_type_name = type_names[selection[0]]
                count = int(count_var.get())
                
                if count <= 0:
                    messagebox.showerror("Hiba", "A darabszám pozitív kell legyen!")
                    return
                
                self.result = {"type": selected_type_name, "count": count}
                dialog.destroy()
                
            except ValueError:
                messagebox.showerror("Hiba", "Érvénytelen darabszám!")
        
        def on_cancel():
            self.result = None
            dialog.destroy()
        
        ttk.Button(button_frame, text="Hozzáadás", command=on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Mégse", command=on_cancel).pack(side=tk.LEFT, padx=5)
        
        # Alapértelmezett kiválasztás
        if type_listbox.size() > 0:
            type_listbox.selection_set(0)
        count_entry.focus()
        
        # Várakozás a dialógus bezárására
        dialog.wait_window()
        
        return self.result
