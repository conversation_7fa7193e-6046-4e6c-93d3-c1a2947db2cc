"""
Összes funkció tesztelése
"""

from dxf_parser import DXFParser
from panel_manager import PanelManager
from standard_sheet_manager import StandardSheetManager


def test_all_features():
    """Összes funkció tesztelése"""
    
    print("=== DXF VIEWER FUNKCIÓ TESZT ===\n")
    
    # 1. DXF betöltés tesztelése
    print("1. DXF fájl betöltése...")
    parser = DXFParser()
    success = parser.load_dxf("GODEFEHER.dxf")
    
    if success:
        entities = parser.get_entities()
        layers = parser.get_layers()
        bounds = parser.get_bounds()
        
        print(f"   ✅ Sikeres betöltés")
        print(f"   📊 {len(entities)} entitás, {len(layers)} réteg")
        print(f"   📏 Rajz mérete: {bounds[2]-bounds[0]:.0f} x {bounds[3]-bounds[1]:.0f}")
    else:
        print("   ❌ Hiba a betöltés során")
        return
    
    # 2. <PERSON><PERSON><PERSON><PERSON><PERSON> tükrözés ellenőrzése
    print("\n2. Koordináta tükrözés ellenőrzése...")
    all_y = []
    for entity in entities:
        if entity['type'] == 'line':
            all_y.extend([entity['start'][1], entity['end'][1]])
    
    if max(all_y) <= 0:
        print("   ✅ Tükrözés sikeres - minden Y koordináta ≤ 0")
    else:
        print("   ❌ Tükrözés hibás - vannak pozitív Y koordináták")
    
    # 3. Panel keresés tesztelése
    print("\n3. Panel (zárt alakzat) keresés...")
    panel_manager = PanelManager()
    panels = panel_manager.find_closed_shapes(entities)
    
    print(f"   ✅ {len(panels)} panel találva")
    
    if panels:
        largest_panel = max(panels, key=lambda p: p.area)
        smallest_panel = min(panels, key=lambda p: p.area)
        print(f"   📊 Legnagyobb panel: {largest_panel.area:.0f} terület")
        print(f"   📊 Legkisebb panel: {smallest_panel.area:.0f} terület")
    
    # 4. Szabványlapok tesztelése
    print("\n4. Szabványlapok kezelése...")
    sheet_manager = StandardSheetManager()
    
    # Alapértelmezett típusok
    sheet_types = sheet_manager.get_sheet_types()
    print(f"   ✅ {len(sheet_types)} alapértelmezett szabványlap típus")
    
    # Új típus hozzáadása
    sheet_manager.add_sheet_type("Teszt GKB", 1000, 2000, "#FF0000")
    print("   ✅ Új típus hozzáadva")
    
    # Lapok hozzáadása
    sheet_manager.add_sheets("GKB 1200x2000", 3)
    sheet_manager.add_sheets("Teszt GKB", 2)
    sheets = sheet_manager.get_sheets()
    print(f"   ✅ {len(sheets)} szabványlap hozzáadva")
    
    # Pozícionálás
    sheet_manager.set_sheet_area_position(bounds)
    print("   ✅ Lapok pozicionálva a DXF rajz mellett")
    
    # 5. Összefoglalás
    print("\n=== ÖSSZEFOGLALÁS ===")
    print(f"✅ DXF fájl: {len(entities)} entitás betöltve")
    print(f"✅ Koordináta tükrözés: működik")
    print(f"✅ Panel felismerés: {len(panels)} panel")
    print(f"✅ Szabványlapok: {len(sheets)} lap")
    print(f"✅ Rétegek: {len(layers)} réteg")
    
    print("\n🎯 Az alkalmazás készen áll a használatra!")
    print("   - Nyisd meg a DXF Viewer-t")
    print("   - Töltsd be a GODEFEHER.dxf fájlt")
    print("   - Használd a 'Panel kiválasztása' funkciót")
    print("   - Adj hozzá szabványlapokat")
    print("   - Mozgasd a lapokat drag & drop-pal")


if __name__ == "__main__":
    test_all_features()
