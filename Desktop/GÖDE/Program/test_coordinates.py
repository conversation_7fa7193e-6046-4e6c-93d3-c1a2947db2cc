"""
Koordináta rendszer tesztelése
"""

from dxf_parser import DXFParser


def test_coordinates():
    """Koordináták tesztelése"""
    
    parser = DXFParser()
    success = parser.load_dxf("GODEFEHER.dxf")
    
    if not success:
        print("Hiba: <PERSON><PERSON> betölteni a DXF fájlt!")
        return
    
    entities = parser.get_entities()
    bounds = parser.get_bounds()
    
    print(f"Rajz határai: {bounds}")
    print(f"Szélesség: {bounds[2] - bounds[0]:.2f}")
    print(f"Magasság: {bounds[3] - bounds[1]:.2f}")
    
    print("\nElső 5 vonal koordinátái:")
    line_count = 0
    for entity in entities:
        if entity['type'] == 'line' and line_count < 5:
            print(f"Vonal {line_count + 1}: {entity['start']} -> {entity['end']}")
            line_count += 1
    
    # Y koordináták ellenőrzése
    all_y = []
    for entity in entities:
        if entity['type'] == 'line':
            all_y.extend([entity['start'][1], entity['end'][1]])
    
    print(f"\nY koordináták tartománya: {min(all_y):.2f} - {max(all_y):.2f}")
    print(f"Y koordináták növekvő sorrendben: {sorted(set(all_y))[:10]}")


if __name__ == "__main__":
    test_coordinates()
