"""
DXF fájl elemzése - milyen entitásokat tartalmaz
"""

import ezdxf
from collections import Counter


def analyze_dxf(filepath):
    """DXF fájl részletes elemzése"""
    try:
        doc = ezdxf.readfile(filepath)
        print(f"DXF fájl elemzése: {filepath}")
        print(f"DXF verzió: {doc.dxfversion}")
        print("-" * 50)
        
        # Modelspace entitások
        modelspace = doc.modelspace()
        entity_types = []
        layer_info = {}
        
        print("ENTITÁSOK TÍPUSAI:")
        for entity in modelspace:
            entity_type = entity.dxftype()
            entity_types.append(entity_type)
            
            # Réteg információ
            layer = entity.dxf.layer
            if layer not in layer_info:
                layer_info[layer] = []
            layer_info[layer].append(entity_type)
            
            # Részletes információ az első néhány entitásról
            if len([t for t in entity_types if t == entity_type]) <= 3:
                print(f"\n{entity_type}:")
                print(f"  Réteg: {layer}")
                print(f"  Szín: {getattr(entity.dxf, 'color', 'BYLAYER')}")
                
                # Specifikus információk
                if entity_type == 'LINE':
                    print(f"  Start: {entity.dxf.start}")
                    print(f"  End: {entity.dxf.end}")
                elif entity_type == 'CIRCLE':
                    print(f"  Center: {entity.dxf.center}")
                    print(f"  Radius: {entity.dxf.radius}")
                elif entity_type == 'ARC':
                    print(f"  Center: {entity.dxf.center}")
                    print(f"  Radius: {entity.dxf.radius}")
                    print(f"  Start angle: {entity.dxf.start_angle}")
                    print(f"  End angle: {entity.dxf.end_angle}")
                elif entity_type == 'LWPOLYLINE':
                    points = list(entity.get_points())
                    print(f"  Pontok száma: {len(points)}")
                    print(f"  Zárt: {entity.closed}")
                    if points:
                        print(f"  Első pont: {points[0]}")
                        print(f"  Utolsó pont: {points[-1]}")
                elif entity_type == 'POLYLINE':
                    vertices = list(entity.vertices)
                    print(f"  Vertices száma: {len(vertices)}")
                    print(f"  Zárt: {entity.is_closed}")
                elif entity_type == 'TEXT':
                    print(f"  Szöveg: '{entity.dxf.text}'")
                    print(f"  Pozíció: {entity.dxf.insert}")
                    print(f"  Magasság: {entity.dxf.height}")
                elif entity_type == 'MTEXT':
                    print(f"  Szöveg: '{entity.text}'")
                    print(f"  Pozíció: {entity.dxf.insert}")
                    print(f"  Magasság: {entity.dxf.char_height}")
                elif entity_type == 'INSERT':
                    print(f"  Block név: {entity.dxf.name}")
                    print(f"  Pozíció: {entity.dxf.insert}")
                    print(f"  Skála: {getattr(entity.dxf, 'xscale', 1)}, {getattr(entity.dxf, 'yscale', 1)}")
                elif entity_type == 'SPLINE':
                    print(f"  Kontroll pontok: {len(entity.control_points)}")
                    print(f"  Degree: {entity.dxf.degree}")
                elif entity_type == 'ELLIPSE':
                    print(f"  Center: {entity.dxf.center}")
                    print(f"  Major axis: {entity.dxf.major_axis}")
                    print(f"  Ratio: {entity.dxf.ratio}")
                elif entity_type == 'HATCH':
                    print(f"  Pattern: {entity.dxf.pattern_name}")
                    print(f"  Paths: {len(entity.paths)}")
                elif entity_type == 'DIMENSION':
                    print(f"  Dimtype: {getattr(entity.dxf, 'dimtype', 'N/A')}")
                elif entity_type == 'LEADER':
                    print(f"  Vertices: {len(entity.vertices) if hasattr(entity, 'vertices') else 'N/A'}")
                
                # Egyéb attribútumok
                print(f"  Egyéb attribútumok: {[attr for attr in dir(entity.dxf) if not attr.startswith('_')]}")
        
        # Összesítés
        print("\n" + "=" * 50)
        print("ENTITÁS TÍPUSOK ÖSSZESÍTÉSE:")
        type_counts = Counter(entity_types)
        for entity_type, count in type_counts.most_common():
            print(f"{entity_type}: {count} db")
        
        print(f"\nÖsszes entitás: {len(entity_types)}")
        
        # Rétegek
        print("\n" + "=" * 50)
        print("RÉTEGEK:")
        for layer, entities in layer_info.items():
            layer_counts = Counter(entities)
            print(f"\n{layer}:")
            for entity_type, count in layer_counts.items():
                print(f"  {entity_type}: {count} db")
        
        # Blokkok
        print("\n" + "=" * 50)
        print("BLOKKOK:")
        for block_name in doc.blocks:
            if not block_name.startswith('*'):  # Skip model/paper space
                block = doc.blocks[block_name]
                block_entities = [entity.dxftype() for entity in block]
                if block_entities:
                    print(f"{block_name}: {Counter(block_entities)}")
        
        return type_counts, layer_info
        
    except Exception as e:
        print(f"Hiba a DXF fájl elemzésekor: {e}")
        return None, None


if __name__ == "__main__":
    analyze_dxf("GODEFEHER.dxf")
