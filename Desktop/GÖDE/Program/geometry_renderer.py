"""
Geometria renderelő modul
Kezeli a DXF elemek rajzolás<PERSON>t a canvas-ra
"""

import tkinter as tk
import math
from typing import List, Dict, Any, Tuple


class GeometryRenderer:
    def __init__(self, canvas: tk.<PERSON>, canvas_manager):
        self.canvas = canvas
        self.canvas_manager = canvas_manager
        self.entities = []
        self.layers = {}
        self.panel_manager = None  # Panel manager referencia
        
        # AutoCAD színpaletta (bővített)
        self.autocad_colors = {
            0: "#000000",  # Fekete
            1: "#FF0000",  # Piros
            2: "#FFFF00",  # Sárga
            3: "#00FF00",  # <PERSON><PERSON><PERSON>
            4: "#00FFFF",  # <PERSON><PERSON><PERSON>
            5: "#0000FF",  # <PERSON>é<PERSON>
            6: "#FF00FF",  # Magenta
            7: "#FFFFFF",  # Fehér
            8: "#808080",  # Szürke
            9: "#C0C0C0",  # Világosszürke
            10: "#FF8080", # Világospiros
            11: "#FFFF80", # Világossárga
            12: "#80FF80", # Világoszöld
            13: "#80FFFF", # Világoscián
            14: "#8080FF", # Világoskék
            15: "#FF80FF", # Világosmagenta
            # Speciális színkódok
            256: "#FFFFFF",  # BYLAYER - fehér alapértelmezett
            257: "#000000",  # BYBLOCK - fekete alapértelmezett
        }
        
        # Alapértelmezett rajzolási beállítások
        self.default_color = "#FFFFFF"  # Fehér alapértelmezett
        self.line_width = 2  # Optimális vonalvastagság
        
        # Canvas manager callback beállítása
        self.canvas_manager.set_update_callback(self.render)
    
    def set_data(self, entities: List[Dict[str, Any]], layers: Dict[str, Dict]):
        """Rajzolandó adatok beállítása"""
        self.entities = entities
        self.layers = layers

    def set_panel_manager(self, panel_manager):
        """Panel manager beállítása"""
        self.panel_manager = panel_manager
    
    def render(self):
        """Teljes újrarajzolás"""
        self.canvas.delete("all")

        if not self.entities:
            return

        # Látható terület meghatározása optimalizáláshoz
        visible_bounds = self.canvas_manager.get_visible_bounds()

        # Entitások rajzolása
        for entity in self.entities:
            if self._is_entity_visible(entity, visible_bounds):
                self._render_entity(entity)

        # Panelek kiemelése
        self._render_panels()

        # Koordináta rendszer rajzolása (opcionális)
        self._draw_coordinate_system()
    
    def _is_entity_visible(self, entity: Dict[str, Any], visible_bounds: Tuple[float, float, float, float]) -> bool:
        """Ellenőrzi, hogy az entitás látható-e a jelenlegi nézetben"""
        min_x, min_y, max_x, max_y = visible_bounds
        
        # Egyszerű bounding box ellenőrzés
        if entity['type'] == 'line':
            start_x, start_y = entity['start']
            end_x, end_y = entity['end']
            entity_min_x = min(start_x, end_x)
            entity_max_x = max(start_x, end_x)
            entity_min_y = min(start_y, end_y)
            entity_max_y = max(start_y, end_y)
        
        elif entity['type'] == 'circle':
            cx, cy = entity['center']
            r = entity['radius']
            entity_min_x = cx - r
            entity_max_x = cx + r
            entity_min_y = cy - r
            entity_max_y = cy + r
        
        elif entity['type'] == 'arc':
            cx, cy = entity['center']
            r = entity['radius']
            entity_min_x = cx - r
            entity_max_x = cx + r
            entity_min_y = cy - r
            entity_max_y = cy + r
        
        elif entity['type'] == 'polyline':
            if not entity['points']:
                return False
            xs = [p[0] for p in entity['points']]
            ys = [p[1] for p in entity['points']]
            entity_min_x = min(xs)
            entity_max_x = max(xs)
            entity_min_y = min(ys)
            entity_max_y = max(ys)
        
        elif entity['type'] == 'text':
            entity_min_x = entity_max_x = entity['position'][0]
            entity_min_y = entity_max_y = entity['position'][1]
        
        else:
            return True  # Ismeretlen típus esetén rajzoljuk ki
        
        # Átfedés ellenőrzése
        return not (entity_max_x < min_x or entity_min_x > max_x or 
                   entity_max_y < min_y or entity_min_y > max_y)
    
    def _render_entity(self, entity: Dict[str, Any]):
        """Egyetlen entitás rajzolása"""
        # Réteg láthatóság ellenőrzése
        layer_name = entity.get('layer', '0')
        if layer_name in self.layers and not self.layers[layer_name].get('visible', True):
            return
        
        # Szín meghatározása
        color = self._get_entity_color(entity)
        
        if entity['type'] == 'line':
            self._draw_line(entity, color)
        elif entity['type'] == 'circle':
            self._draw_circle(entity, color)
        elif entity['type'] == 'arc':
            self._draw_arc(entity, color)
        elif entity['type'] == 'polyline':
            self._draw_polyline(entity, color)
        elif entity['type'] == 'text':
            self._draw_text(entity, color)
    
    def _get_entity_color(self, entity: Dict[str, Any]) -> str:
        """Entitás színének meghatározása"""
        color_index = entity.get('color', 7)

        # Speciális színkódok kezelése
        if color_index == 256:  # BYLAYER
            # Réteg színének használata, ha van
            layer_name = entity.get('layer', '0')
            if layer_name in self.layers:
                layer_color = self.layers[layer_name].get('color', 7)
                if layer_color != 256:  # Ha a réteg színe nem BYLAYER
                    return self.autocad_colors.get(layer_color, self.default_color)
            # Alapértelmezett fehér, ha nincs réteg szín
            return "#FFFFFF"
        elif color_index == 257:  # BYBLOCK
            return "#000000"  # Fekete alapértelmezett

        # Normál színkód
        return self.autocad_colors.get(color_index, self.default_color)
    
    def _draw_line(self, entity: Dict[str, Any], color: str):
        """Vonal rajzolása"""
        start_x, start_y = self.canvas_manager.world_to_canvas(*entity['start'])
        end_x, end_y = self.canvas_manager.world_to_canvas(*entity['end'])

        self.canvas.create_line(
            start_x, start_y, end_x, end_y,
            fill=color, width=self.line_width
        )
    
    def _draw_circle(self, entity: Dict[str, Any], color: str):
        """Kör rajzolása"""
        center_x, center_y = self.canvas_manager.world_to_canvas(*entity['center'])
        radius_canvas = entity['radius'] * self.canvas_manager.scale
        
        self.canvas.create_oval(
            center_x - radius_canvas, center_y - radius_canvas,
            center_x + radius_canvas, center_y + radius_canvas,
            outline=color, width=self.line_width, fill=""
        )
    
    def _draw_arc(self, entity: Dict[str, Any], color: str):
        """Ív rajzolása"""
        center_x, center_y = self.canvas_manager.world_to_canvas(*entity['center'])
        radius_canvas = entity['radius'] * self.canvas_manager.scale
        
        # Szögek konvertálása fokra (tkinter fokban dolgozik)
        start_angle = math.degrees(entity['start_angle'])
        end_angle = math.degrees(entity['end_angle'])
        
        # Ív hosszának kiszámítása
        if end_angle < start_angle:
            extent = end_angle + 360 - start_angle
        else:
            extent = end_angle - start_angle
        
        self.canvas.create_arc(
            center_x - radius_canvas, center_y - radius_canvas,
            center_x + radius_canvas, center_y + radius_canvas,
            start=start_angle, extent=extent,
            outline=color, width=self.line_width, style="arc"
        )
    
    def _draw_polyline(self, entity: Dict[str, Any], color: str):
        """Polyline rajzolása"""
        if len(entity['points']) < 2:
            return
        
        # Pontok konvertálása canvas koordinátákra
        canvas_points = []
        for point in entity['points']:
            canvas_x, canvas_y = self.canvas_manager.world_to_canvas(*point)
            canvas_points.extend([canvas_x, canvas_y])
        
        # Zárt polyline esetén az utolsó pont visszakötése az elsőhöz
        if entity.get('closed', False) and len(entity['points']) > 2:
            first_point = self.canvas_manager.world_to_canvas(*entity['points'][0])
            canvas_points.extend(first_point)
        
        self.canvas.create_line(
            *canvas_points,
            fill=color, width=self.line_width, smooth=False
        )
    
    def _draw_text(self, entity: Dict[str, Any], color: str):
        """Szöveg rajzolása"""
        pos_x, pos_y = self.canvas_manager.world_to_canvas(*entity['position'])
        
        # Szöveg méret skálázása
        font_size = max(8, int(entity.get('height', 10) * self.canvas_manager.scale))
        
        self.canvas.create_text(
            pos_x, pos_y,
            text=entity['text'],
            fill=color,
            font=("Arial", font_size),
            anchor="sw"
        )
    
    def _draw_coordinate_system(self):
        """Koordináta rendszer rajzolása (opcionális)"""
        # Csak nagy zoom szinten jelenítjük meg
        if self.canvas_manager.scale < 0.1:
            return
        
        visible_bounds = self.canvas_manager.get_visible_bounds()
        min_x, min_y, max_x, max_y = visible_bounds
        
        # Rács lépésköz meghatározása
        grid_size = self._calculate_grid_size()
        
        # Függőleges vonalak
        start_x = math.floor(min_x / grid_size) * grid_size
        x = start_x
        while x <= max_x:
            canvas_x, _ = self.canvas_manager.world_to_canvas(x, 0)
            _, canvas_min_y = self.canvas_manager.world_to_canvas(0, min_y)
            _, canvas_max_y = self.canvas_manager.world_to_canvas(0, max_y)
            
            self.canvas.create_line(
                canvas_x, canvas_min_y, canvas_x, canvas_max_y,
                fill="#333333", width=1, stipple="gray25"
            )
            x += grid_size
        
        # Vízszintes vonalak
        start_y = math.floor(min_y / grid_size) * grid_size
        y = start_y
        while y <= max_y:
            _, canvas_y = self.canvas_manager.world_to_canvas(0, y)
            canvas_min_x, _ = self.canvas_manager.world_to_canvas(min_x, 0)
            canvas_max_x, _ = self.canvas_manager.world_to_canvas(max_x, 0)
            
            self.canvas.create_line(
                canvas_min_x, canvas_y, canvas_max_x, canvas_y,
                fill="#333333", width=1, stipple="gray25"
            )
            y += grid_size
    
    def _calculate_grid_size(self) -> float:
        """Rács méretének kiszámítása a zoom szint alapján"""
        scale = self.canvas_manager.scale
        
        # Alapértelmezett rács méretek
        grid_sizes = [0.1, 0.5, 1, 2, 5, 10, 20, 50, 100, 200, 500, 1000]
        
        # Megfelelő rács méret kiválasztása
        target_pixel_size = 50  # pixel
        world_size = target_pixel_size / scale
        
        for grid_size in grid_sizes:
            if grid_size >= world_size:
                return grid_size
        
        return grid_sizes[-1]
    
    def _render_panels(self):
        """Panelek kiemelő renderelése"""
        if not self.panel_manager:
            return

        # Csak kiválasztási módban jelenítjük meg a paneleket
        if not self.panel_manager.is_selection_mode():
            return

        for panel in self.panel_manager.get_panels():
            if panel.is_active:
                # Aktív panel kiemelése
                self._highlight_panel(panel, "#FF0000", 5)  # Piros, vastag keret
            else:
                # Inaktív panelek jelzése
                self._highlight_panel(panel, "#00FF00", 2)  # Zöld, közepes keret

    def _highlight_panel(self, panel, color: str, width: int):
        """Panel kiemelése az eredeti alakzat kontúrja mentén"""
        # Panel eredeti vonalainak kiemelése
        for entity in panel.entities:
            if entity['type'] == 'line':
                start_x, start_y = self.canvas_manager.world_to_canvas(*entity['start'])
                end_x, end_y = self.canvas_manager.world_to_canvas(*entity['end'])

                # Kiemelő vonal az eredeti vonal fölé
                self.canvas.create_line(
                    start_x, start_y, end_x, end_y,
                    fill=color, width=width + 2, dash=(10, 5)
                )

        # Panel ID megjelenítése a középpontban
        center_x, center_y = self.canvas_manager.world_to_canvas(*panel.center)

        # Háttér téglalap a szöveg mögé
        text_width = 30
        text_height = 16
        self.canvas.create_rectangle(
            center_x - text_width//2, center_y - text_height//2,
            center_x + text_width//2, center_y + text_height//2,
            fill="#000000", outline=color, width=2
        )

        # Panel ID szöveg
        self.canvas.create_text(
            center_x, center_y,
            text=f"P{panel.id}",
            fill=color,
            font=("Arial", 9, "bold"),
            anchor="center"
        )

    def toggle_layer_visibility(self, layer_name: str):
        """Réteg láthatóságának váltása"""
        if layer_name in self.layers:
            self.layers[layer_name]['visible'] = not self.layers[layer_name]['visible']
            self.render()
