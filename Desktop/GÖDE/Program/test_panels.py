"""
Panel keresés tesztelése
"""

from dxf_parser import DXFParser
from panel_manager import PanelManager


def test_panel_detection():
    """Panel keresés tesztelése a GODEFEHER.dxf fájlon"""
    
    # DXF betöltése
    parser = DXFParser()
    success = parser.load_dxf("GODEFEHER.dxf")
    
    if not success:
        print("Hiba: Nem sikerült betölteni a DXF fájlt!")
        return
    
    entities = parser.get_entities()
    print(f"Betöltött entitások: {len(entities)}")
    
    # Panel manager
    panel_manager = PanelManager()
    panels = panel_manager.find_closed_shapes(entities)
    
    print(f"Talált panelek: {len(panels)}")
    
    for i, panel in enumerate(panels):
        print(f"\nPanel {i}:")
        print(f"  ID: {panel.id}")
        print(f"  Entitások száma: {len(panel.entities)}")
        print(f"  Határok: {panel.bounds}")
        print(f"  Terület: {panel.area:.2f}")
        print(f"  Kerület: {panel.perimeter:.2f}")
        print(f"  Középpont: {panel.center}")


if __name__ == "__main__":
    test_panel_detection()
